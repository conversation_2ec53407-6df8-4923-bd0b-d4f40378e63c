import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';

// GET - 获取用户设置
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID为必填项',
        },
        { status: 400 }
      );
    }

    const settings = await executeQuery<any[]>(
      'SELECT * FROM user_settings WHERE user_id = ?',
      [userId]
    );

    if (settings.length === 0) {
      // 如果没有设置记录，创建默认设置
      const id = generateId();
      const now = new Date();
      const defaultSettings = {
        id,
        user_id: userId,
        current_provider: 'google',
        current_model: 'gemini-2.5-flash-preview-05-20',
        theme: 'light',
        language: 'zh-CN',
        font_size: 'medium',
        auto_save: true,
        notifications: true,
        created_at: now,
        updated_at: now,
      };

      const query = `
        INSERT INTO user_settings (
          id, user_id, current_provider, current_model, theme, language,
          font_size, auto_save, notifications, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        id,
        userId,
        defaultSettings.current_provider,
        defaultSettings.current_model,
        defaultSettings.theme,
        defaultSettings.language,
        defaultSettings.font_size,
        defaultSettings.auto_save,
        defaultSettings.notifications,
        now,
        now,
      ];

      await executeQuery(query, params);

      // 转换返回的默认设置为 camelCase
      const convertedDefaultSettings = {
        id: defaultSettings.id,
        userId: defaultSettings.user_id,
        currentProvider: defaultSettings.current_provider,
        currentModel: defaultSettings.current_model,
        theme: defaultSettings.theme,
        language: defaultSettings.language,
        fontSize: defaultSettings.font_size,
        autoSave: defaultSettings.auto_save,
        notifications: defaultSettings.notifications,
        createdAt: defaultSettings.created_at,
        updatedAt: defaultSettings.updated_at,
      };

      return NextResponse.json({
        success: true,
        data: convertedDefaultSettings,
      });
    }

    // 转换字段名从 snake_case 到 camelCase
    const setting = settings[0] as any;
    const convertedSetting = {
      ...setting,
      currentProvider: setting.current_provider,
      currentModel: setting.current_model,
      fontSize: setting.font_size,
      autoSave: setting.auto_save,
      createdAt: setting.created_at,
      updatedAt: setting.updated_at,
      userId: setting.user_id
    };

    // 删除原始的 snake_case 字段
    delete convertedSetting.current_provider;
    delete convertedSetting.current_model;
    delete convertedSetting.font_size;
    delete convertedSetting.auto_save;
    delete convertedSetting.created_at;
    delete convertedSetting.updated_at;
    delete convertedSetting.user_id;

    return NextResponse.json({
      success: true,
      data: convertedSetting,
    });
  } catch (error) {
    console.error('获取用户设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取设置失败',
      },
      { status: 500 }
    );
  }
}

// POST - 更新用户设置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      currentProvider,
      currentModel,
      theme,
      language,
      fontSize,
      autoSave,
      notifications,
    } = body;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID为必填项',
        },
        { status: 400 }
      );
    }

    // 检查是否已存在设置
    const existingSettings = await executeQuery<any[]>(
      'SELECT id FROM user_settings WHERE user_id = ?',
      [userId]
    );

    if (existingSettings.length > 0) {
      // 更新现有设置
      const query = `
        UPDATE user_settings 
        SET current_provider = ?, current_model = ?, theme = ?, language = ?,
            font_size = ?, auto_save = ?, notifications = ?, updated_at = ?
        WHERE user_id = ?
      `;

      const params = [
        currentProvider || null,
        currentModel || null,
        theme || null,
        language || null,
        fontSize || null,
        autoSave !== undefined ? Boolean(autoSave) : null,
        notifications !== undefined ? Boolean(notifications) : null,
        new Date(),
        userId,
      ];

      await executeQuery(query, params);
    } else {
      // 创建新设置
      const id = generateId();
      const now = new Date();

      const query = `
        INSERT INTO user_settings (
          id, user_id, current_provider, current_model, theme, language,
          font_size, auto_save, notifications, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        id,
        userId,
        currentProvider || 'google',
        currentModel || 'gemini-2.5-flash-preview-05-20',
        theme || 'light',
        language || 'zh-CN',
        fontSize || 'medium',
        Boolean(autoSave !== undefined ? autoSave : true),
        Boolean(notifications !== undefined ? notifications : true),
        now,
        now,
      ];

      await executeQuery(query, params);
    }

    return NextResponse.json({
      success: true,
      message: '设置保存成功',
    });
  } catch (error) {
    console.error('保存用户设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '保存设置失败',
      },
      { status: 500 }
    );
  }
}

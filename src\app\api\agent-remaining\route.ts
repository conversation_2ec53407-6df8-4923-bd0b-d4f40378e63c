import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 获取用户对特定智能体的剩余次数
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const agentId = searchParams.get('agentId');

    if (!userId || !agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID和智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 1. 获取智能体信息
    const agentQuery = `
      SELECT id, name, trial_usage_count 
      FROM agents 
      WHERE id = ? AND enabled = true
      LIMIT 1
    `;
    const agentResult = await executeQuery<any[]>(agentQuery, [agentId]);
    
    if (agentResult.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体不存在或已禁用',
        },
        { status: 404 }
      );
    }

    const agent = agentResult[0];
    const trialCount = agent.trial_usage_count || 3;

    // 2. 检查用户是否有有效的付费订阅
    const subscriptionQuery = `
      SELECT
        id,
        remaining_usage,
        end_date,
        status,
        pricing_plan_id
      FROM user_agent_subscriptions
      WHERE user_id = ?
        AND agent_id = ?
        AND status = 'active'
        AND (
          (remaining_usage > 0) OR
          (end_date IS NOT NULL AND end_date > NOW())
        )
      ORDER BY created_at DESC
      LIMIT 1
    `;
    const subscriptionResult = await executeQuery<any[]>(subscriptionQuery, [userId, agentId]);

    // 3. 检查用户是否在当前智能体下使用过兑换码
    const redemptionQuery = `
      SELECT
        rl.id,
        rl.code_type,
        rl.duration_days,
        rl.usage_count,
        rl.redeemed_at,
        rc.label
      FROM redemption_logs rl
      JOIN redemption_codes rc ON rl.redemption_code_id = rc.id
      WHERE rl.user_id = ? AND rl.agent_id = ?
      ORDER BY rl.redeemed_at DESC
      LIMIT 1
    `;
    const redemptionResult = await executeQuery<any[]>(redemptionQuery, [userId, agentId]);

    const hasUsedRedemptionCode = redemptionResult.length > 0;
    const latestRedemption = hasUsedRedemptionCode ? redemptionResult[0] : null;

    // 如果有付费订阅
    if (subscriptionResult.length > 0) {
      const subscription = subscriptionResult[0];

      // 检查是否是兑换码生成的订阅
      const isRedemptionSubscription = subscription.pricing_plan_id === 'redemption_duration' ||
                                       subscription.pricing_plan_id === 'redemption_usage';

      return NextResponse.json({
        success: true,
        data: {
          agentId,
          agentName: agent.name,
          hasSubscription: true,
          subscriptionType: subscription.end_date ? 'time_based' : 'usage_based',
          remainingCount: subscription.remaining_usage || null, // null 表示无限制（时间订阅）
          expiryDate: subscription.end_date,
          trialCount,
          status: 'subscribed',
          isRedemptionUser: isRedemptionSubscription || hasUsedRedemptionCode,
          redemptionInfo: latestRedemption
        }
      });
    }

    // 4. 获取试用使用情况
    const usageQuery = `
      SELECT
        usage_count,
        remaining_count,
        created_at
      FROM agent_usage_logs
      WHERE user_id = ? AND agent_id = ?
      ORDER BY created_at DESC
      LIMIT 1
    `;
    const usageResult = await executeQuery<any[]>(usageQuery, [userId, agentId]);

    let usedCount = 0;
    let remainingCount = trialCount;

    if (usageResult.length > 0) {
      const usage = usageResult[0];
      usedCount = usage.usage_count || 0;
      remainingCount = usage.remaining_count !== null ? usage.remaining_count : Math.max(0, trialCount - usedCount);
    }

    // 如果用户使用过兑换码，显示为会员状态
    if (hasUsedRedemptionCode) {
      return NextResponse.json({
        success: true,
        data: {
          agentId,
          agentName: agent.name,
          hasSubscription: false, // 技术上不是付费订阅，但显示为会员
          trialCount,
          usedCount,
          remainingCount,
          status: remainingCount > 0 ? 'trial_available' : 'trial_exhausted',
          isRedemptionUser: true,
          redemptionInfo: latestRedemption
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        agentId,
        agentName: agent.name,
        hasSubscription: false,
        trialCount,
        usedCount,
        remainingCount,
        status: remainingCount > 0 ? 'trial_available' : 'trial_exhausted',
        isRedemptionUser: false
      }
    });

  } catch (error) {
    console.error('获取剩余次数失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取剩余次数失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

// POST - 检查用户是否可以使用智能体（在发送消息前调用）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, agentId } = body;

    if (!userId || !agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID和智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 创建带参数的GET请求
    const getUrl = new URL(request.url);
    getUrl.searchParams.set('userId', userId);
    getUrl.searchParams.set('agentId', agentId);

    const getRequest = new NextRequest(getUrl.toString(), {
      method: 'GET',
      headers: request.headers,
    });

    const getResponse = await GET(getRequest);
    const getData = await getResponse.json();

    if (!getData.success) {
      return NextResponse.json(getData, { status: getResponse.status });
    }

    const { hasSubscription, remainingCount, status } = getData.data;

    // 判断是否可以使用
    const canUse = hasSubscription || (remainingCount !== null && remainingCount > 0);

    return NextResponse.json({
      success: true,
      data: {
        canUse,
        reason: !canUse ? 'trial_exhausted' : null,
        ...getData.data
      }
    });

  } catch (error) {
    console.error('检查使用权限失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '检查使用权限失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

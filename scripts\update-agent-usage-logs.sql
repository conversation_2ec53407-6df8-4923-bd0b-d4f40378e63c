-- 更新 agent_usage_logs 表结构
-- 添加次数字段和截止时间字段

-- 1. 添加次数字段（用于记录本次使用的次数，通常为1）
ALTER TABLE agent_usage_logs 
ADD COLUMN IF NOT EXISTS usage_count INT DEFAULT 1 COMMENT '本次使用次数';

-- 2. 添加截止时间字段（用于定时定价模式）
ALTER TABLE agent_usage_logs 
ADD COLUMN IF NOT EXISTS expiry_date TIMESTAMP NULL COMMENT '截止时间，用于定时定价模式';

-- 3. 添加索引以提高查询性能
ALTER TABLE agent_usage_logs 
ADD INDEX IF NOT EXISTS idx_expiry_date (expiry_date);

-- 4. 更新现有记录的 usage_count 为 1（如果为 NULL 或 0）
UPDATE agent_usage_logs 
SET usage_count = 1 
WHERE usage_count IS NULL OR usage_count = 0;

-- 5. 创建用户智能体使用统计视图（可选，用于快速查询）
CREATE OR REPLACE VIEW user_agent_usage_summary AS
SELECT 
    user_id,
    agent_id,
    COUNT(*) as total_sessions,
    SUM(usage_count) as total_usage_count,
    SUM(tokens_used) as total_tokens,
    SUM(cost) as total_cost,
    MIN(created_at) as first_usage,
    MAX(created_at) as last_usage,
    -- 检查是否有有效的定时订阅
    MAX(CASE 
        WHEN expiry_date IS NOT NULL AND expiry_date > NOW() 
        THEN expiry_date 
        ELSE NULL 
    END) as active_expiry_date
FROM agent_usage_logs 
GROUP BY user_id, agent_id;

-- 6. 创建存储过程：购买定时套餐时设置截止日期
DELIMITER //

CREATE OR REPLACE PROCEDURE SetTimedSubscriptionExpiry(
    IN p_user_id VARCHAR(50),
    IN p_agent_id VARCHAR(50),
    IN p_duration_days INT
)
BEGIN
    DECLARE v_expiry_date TIMESTAMP;
    
    -- 计算截止日期
    SET v_expiry_date = DATE_ADD(NOW(), INTERVAL p_duration_days DAY);
    
    -- 更新该用户该智能体的所有未来使用记录的截止时间
    -- 注意：这里只是示例，实际应用中可能需要更复杂的逻辑
    UPDATE agent_usage_logs 
    SET expiry_date = v_expiry_date
    WHERE user_id = p_user_id 
      AND agent_id = p_agent_id 
      AND created_at >= NOW()
      AND expiry_date IS NULL;
      
    -- 返回设置的截止日期
    SELECT v_expiry_date as expiry_date;
END //

DELIMITER ;

-- 7. 创建函数：检查用户是否有有效的定时订阅
DELIMITER //

CREATE OR REPLACE FUNCTION CheckTimedSubscriptionValid(
    p_user_id VARCHAR(50),
    p_agent_id VARCHAR(50)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- 检查是否有有效的定时订阅
    SELECT COUNT(*) INTO v_count
    FROM agent_usage_logs 
    WHERE user_id = p_user_id 
      AND agent_id = p_agent_id 
      AND expiry_date IS NOT NULL 
      AND expiry_date > NOW()
    LIMIT 1;
    
    RETURN v_count > 0;
END //

DELIMITER ;

-- 8. 创建触发器：自动设置使用次数默认值
DELIMITER //

CREATE OR REPLACE TRIGGER tr_agent_usage_logs_before_insert
BEFORE INSERT ON agent_usage_logs
FOR EACH ROW
BEGIN
    -- 如果 usage_count 为 NULL 或 0，设置为 1
    IF NEW.usage_count IS NULL OR NEW.usage_count = 0 THEN
        SET NEW.usage_count = 1;
    END IF;
END //

DELIMITER ;

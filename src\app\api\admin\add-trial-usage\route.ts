import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始为智能体表添加试用次数字段...');

    // 1. 添加试用次数字段
    try {
      const addTrialUsageQuery = `
        ALTER TABLE agents
        ADD COLUMN trial_usage_count INT DEFAULT 3 COMMENT '试用次数，用户可以免费试用的次数'
      `;

      console.log('添加 trial_usage_count 字段...');
      await executeQuery(addTrialUsageQuery);
      console.log('✓ trial_usage_count 字段添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ trial_usage_count 字段已存在，跳过添加');
      } else {
        console.error('添加 trial_usage_count 字段失败:', error);
        throw error;
      }
    }

    // 2. 添加索引
    try {
      const addIndexQuery = `
        ALTER TABLE agents
        ADD INDEX idx_trial_usage_count (trial_usage_count)
      `;

      console.log('添加索引...');
      await executeQuery(addIndexQuery);
      console.log('✓ 索引添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate key name')) {
        console.log('✓ 索引已存在，跳过添加');
      } else {
        console.error('添加索引失败:', error);
        throw error;
      }
    }

    // 3. 更新现有智能体的试用次数
    const updateQueries = [
      // 设置默认值
      `UPDATE agents SET trial_usage_count = 3 WHERE trial_usage_count IS NULL`,
      
      // 为不同类型的智能体设置不同的默认试用次数
      `UPDATE agents SET trial_usage_count = 5 WHERE category = 'general' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 3 WHERE category = 'programming' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 4 WHERE category = 'writing' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 6 WHERE category = 'education' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 2 WHERE category = 'business' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 4 WHERE category = 'creative' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 3 WHERE category = 'analysis' AND trial_usage_count = 3`,
      `UPDATE agents SET trial_usage_count = 5 WHERE category = 'translation' AND trial_usage_count = 3`,
    ];

    console.log('更新现有智能体的试用次数...');
    for (const query of updateQueries) {
      try {
        const result = await executeQuery(query);
        console.log(`✓ 更新查询执行成功，影响行数: ${result.affectedRows || 0}`);
      } catch (error) {
        console.error('更新查询失败:', error);
        throw error;
      }
    }

    console.log('✅ 智能体试用次数字段添加完成');

    return NextResponse.json({
      success: true,
      message: '智能体试用次数字段添加成功',
    });
  } catch (error) {
    console.error('添加试用次数字段失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '添加试用次数字段失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const WECHAT_APP_ID = process.env.WECHAT_APP_ID || '';
const WECHAT_APP_SECRET = process.env.WECHAT_APP_SECRET || '';

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// GET - 微信登录授权
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');

    if (!code) {
      // 重定向到微信授权页面
      const redirectUri = encodeURIComponent(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/oauth/wechat`);
      const wechatAuthUrl = `https://open.weixin.qq.com/connect/qrconnect?appid=${WECHAT_APP_ID}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_login&state=${state || 'default'}`;
      
      return NextResponse.redirect(wechatAuthUrl);
    }

    // 使用code获取access_token
    const tokenResponse = await fetch(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${WECHAT_APP_ID}&secret=${WECHAT_APP_SECRET}&code=${code}&grant_type=authorization_code`);
    const tokenData = await tokenResponse.json();

    if (tokenData.errcode) {
      return NextResponse.redirect(new URL('/auth?error=wechat_auth_failed', request.url));
    }

    const { access_token, openid } = tokenData;

    // 获取用户信息
    const userResponse = await fetch(`https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}`);
    const userData = await userResponse.json();

    if (userData.errcode) {
      return NextResponse.redirect(new URL('/auth?error=wechat_userinfo_failed', request.url));
    }

    // 检查用户是否已存在
    const existingUsers = await executeQuery(
      'SELECT * FROM users WHERE provider = ? AND provider_id = ?',
      ['wechat', openid]
    );

    let user;
    if (existingUsers.length > 0) {
      // 用户已存在，更新登录信息
      user = existingUsers[0];
      await executeQuery(
        'UPDATE users SET last_login_at = ?, login_count = login_count + 1 WHERE id = ?',
        [new Date(), user.id]
      );
    } else {
      // 创建新用户
      const userId = generateId();
      const now = new Date();

      const query = `
        INSERT INTO users (
          id, username, email, avatar, is_verified, provider, provider_id,
          created_at, updated_at, last_login_at, login_count
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        userId,
        userData.nickname || `微信用户_${openid.slice(-6)}`,
        `wechat_${openid}@temp.com`, // 临时邮箱
        userData.headimgurl || null,
        true, // 微信用户默认已验证
        'wechat',
        openid,
        now,
        now,
        now,
        1,
      ];

      await executeQuery(query, params);

      user = {
        id: userId,
        username: userData.nickname || `微信用户_${openid.slice(-6)}`,
        email: `wechat_${openid}@temp.com`,
        avatar: userData.headimgurl || null,
        provider: 'wechat',
      };
    }

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '7d' });

    // 重定向到前端，带上token
    const redirectUrl = new URL('/auth/callback', request.url);
    redirectUrl.searchParams.set('token', token);
    redirectUrl.searchParams.set('user', JSON.stringify({
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      provider: user.provider,
    }));

    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error('微信登录失败:', error);
    return NextResponse.redirect(new URL('/auth?error=wechat_login_failed', request.url));
  }
}

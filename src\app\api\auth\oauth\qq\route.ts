import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const QQ_APP_ID = process.env.QQ_APP_ID || '';
const QQ_APP_KEY = process.env.QQ_APP_KEY || '';

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// GET - QQ登录授权
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');

    if (!code) {
      // 重定向到QQ授权页面
      const redirectUri = encodeURIComponent(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/oauth/qq`);
      const qqAuthUrl = `https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=${QQ_APP_ID}&redirect_uri=${redirectUri}&state=${state || 'default'}&scope=get_user_info`;
      
      return NextResponse.redirect(qqAuthUrl);
    }

    // 使用code获取access_token
    const redirectUri = encodeURIComponent(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/oauth/qq`);
    const tokenResponse = await fetch(`https://graph.qq.com/oauth2.0/token?grant_type=authorization_code&client_id=${QQ_APP_ID}&client_secret=${QQ_APP_KEY}&code=${code}&redirect_uri=${redirectUri}`);
    const tokenText = await tokenResponse.text();

    // 解析access_token (QQ返回的是URL参数格式)
    const tokenParams = new URLSearchParams(tokenText);
    const accessToken = tokenParams.get('access_token');

    if (!accessToken) {
      return NextResponse.redirect(new URL('/auth?error=qq_auth_failed', request.url));
    }

    // 获取openid
    const openidResponse = await fetch(`https://graph.qq.com/oauth2.0/me?access_token=${accessToken}`);
    const openidText = await openidResponse.text();
    
    // 解析openid (QQ返回的是JSONP格式)
    const openidMatch = openidText.match(/openid":"([^"]+)"/);
    const openid = openidMatch ? openidMatch[1] : null;

    if (!openid) {
      return NextResponse.redirect(new URL('/auth?error=qq_openid_failed', request.url));
    }

    // 获取用户信息
    const userResponse = await fetch(`https://graph.qq.com/user/get_user_info?access_token=${accessToken}&oauth_consumer_key=${QQ_APP_ID}&openid=${openid}`);
    const userData = await userResponse.json();

    if (userData.ret !== 0) {
      return NextResponse.redirect(new URL('/auth?error=qq_userinfo_failed', request.url));
    }

    // 检查用户是否已存在
    const existingUsers = await executeQuery(
      'SELECT * FROM users WHERE provider = ? AND provider_id = ?',
      ['qq', openid]
    );

    let user;
    if (existingUsers.length > 0) {
      // 用户已存在，更新登录信息
      user = existingUsers[0];
      await executeQuery(
        'UPDATE users SET last_login_at = ?, login_count = login_count + 1 WHERE id = ?',
        [new Date(), user.id]
      );
    } else {
      // 创建新用户
      const userId = generateId();
      const now = new Date();

      const query = `
        INSERT INTO users (
          id, username, email, avatar, is_verified, provider, provider_id,
          created_at, updated_at, last_login_at, login_count
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        userId,
        userData.nickname || `QQ用户_${openid.slice(-6)}`,
        `qq_${openid}@temp.com`, // 临时邮箱
        userData.figureurl_qq_2 || userData.figureurl_qq_1 || null,
        true, // QQ用户默认已验证
        'qq',
        openid,
        now,
        now,
        now,
        1,
      ];

      await executeQuery(query, params);

      user = {
        id: userId,
        username: userData.nickname || `QQ用户_${openid.slice(-6)}`,
        email: `qq_${openid}@temp.com`,
        avatar: userData.figureurl_qq_2 || userData.figureurl_qq_1 || null,
        provider: 'qq',
      };
    }

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '7d' });

    // 重定向到前端，带上token
    const redirectUrl = new URL('/auth/callback', request.url);
    redirectUrl.searchParams.set('token', token);
    redirectUrl.searchParams.set('user', JSON.stringify({
      id: user.id,
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      provider: user.provider,
    }));

    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error('QQ登录失败:', error);
    return NextResponse.redirect(new URL('/auth?error=qq_login_failed', request.url));
  }
}

-- 为 agent_usage_logs 表添加剩余次数字段
-- 这个字段用于跟踪用户对特定智能体的剩余使用次数

-- 1. 添加剩余次数字段
ALTER TABLE agent_usage_logs 
ADD COLUMN IF NOT EXISTS remaining_count INT DEFAULT NULL COMMENT '剩余使用次数，NULL表示无限制（已付费）';

-- 2. 添加索引以提高查询性能
ALTER TABLE agent_usage_logs 
ADD INDEX IF NOT EXISTS idx_remaining_count (remaining_count);

-- 3. 创建函数：计算用户对特定智能体的剩余次数
DELIMITER //

CREATE OR REPLACE FUNCTION CalculateRemainingCount(
    p_user_id VARCHAR(50),
    p_agent_id VARCHAR(50)
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_trial_count INT DEFAULT 3;
    DECLARE v_used_count INT DEFAULT 0;
    DECLARE v_has_subscription BOOLEAN DEFAULT FALSE;
    DECLARE v_remaining INT DEFAULT 0;
    
    -- 获取智能体的试用次数
    SELECT trial_usage_count INTO v_trial_count
    FROM agents 
    WHERE id = p_agent_id
    LIMIT 1;
    
    -- 如果没有找到智能体，设置默认试用次数
    IF v_trial_count IS NULL THEN
        SET v_trial_count = 3;
    END IF;
    
    -- 检查用户是否有有效的付费订阅
    SELECT COUNT(*) > 0 INTO v_has_subscription
    FROM user_agent_subscriptions 
    WHERE user_id = p_user_id 
      AND agent_id = p_agent_id 
      AND status = 'active'
      AND (
          -- 按次计费且有剩余次数
          (remaining_usage > 0) OR
          -- 按时计费且未过期
          (end_date IS NOT NULL AND end_date > NOW())
      )
    LIMIT 1;
    
    -- 如果有付费订阅，返回 NULL 表示无限制或根据订阅计算
    IF v_has_subscription THEN
        RETURN NULL;
    END IF;
    
    -- 获取已使用次数（累计的 usage_count）
    SELECT COALESCE(SUM(usage_count), 0) INTO v_used_count
    FROM agent_usage_logs 
    WHERE user_id = p_user_id 
      AND agent_id = p_agent_id;
    
    -- 计算剩余次数
    SET v_remaining = v_trial_count - v_used_count;
    
    -- 确保剩余次数不为负数
    IF v_remaining < 0 THEN
        SET v_remaining = 0;
    END IF;
    
    RETURN v_remaining;
END //

DELIMITER ;

-- 4. 创建触发器：在插入或更新使用记录时自动计算剩余次数
DELIMITER //

CREATE OR REPLACE TRIGGER tr_agent_usage_logs_update_remaining
BEFORE INSERT ON agent_usage_logs
FOR EACH ROW
BEGIN
    -- 计算并设置剩余次数
    SET NEW.remaining_count = CalculateRemainingCount(NEW.user_id, NEW.agent_id);
END //

DELIMITER ;

-- 5. 创建触发器：在更新使用记录时重新计算剩余次数
DELIMITER //

CREATE OR REPLACE TRIGGER tr_agent_usage_logs_update_remaining_on_update
BEFORE UPDATE ON agent_usage_logs
FOR EACH ROW
BEGIN
    -- 重新计算并设置剩余次数
    SET NEW.remaining_count = CalculateRemainingCount(NEW.user_id, NEW.agent_id);
END //

DELIMITER ;

-- 6. 更新现有记录的剩余次数
-- 注意：这个操作可能需要一些时间，取决于数据量
UPDATE agent_usage_logs 
SET remaining_count = CalculateRemainingCount(user_id, agent_id)
WHERE remaining_count IS NULL;

-- 7. 创建视图：方便查询用户的智能体使用情况
CREATE OR REPLACE VIEW user_agent_usage_summary AS
SELECT 
    aul.user_id,
    aul.agent_id,
    a.name as agent_name,
    a.trial_usage_count,
    COALESCE(SUM(aul.usage_count), 0) as total_used,
    aul.remaining_count,
    CASE 
        WHEN aul.remaining_count IS NULL THEN '已付费'
        WHEN aul.remaining_count > 0 THEN '试用中'
        ELSE '试用已用完'
    END as status,
    MAX(aul.created_at) as last_used_at
FROM agent_usage_logs aul
JOIN agents a ON aul.agent_id = a.id
GROUP BY aul.user_id, aul.agent_id, a.name, a.trial_usage_count, aul.remaining_count;

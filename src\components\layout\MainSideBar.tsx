'use client';

import { SideNav } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { memo } from 'react';

import MainSideBarTopActions from './MainSideBarTopActions';
import MainSideBarBottomActions from './MainSideBarBottomActions';
import MainSideBarAvatar from './MainSideBarAvatar';

const MainSideBar = memo(() => {
  const theme = useTheme();

  return (
    <SideNav
      avatar={<MainSideBarAvatar />}
      bottomActions={<MainSideBarBottomActions />}
      style={{
        height: '100vh',
        minHeight: '100vh',
        position: 'sticky',
        top: 0,
        zIndex: 100,
        background: theme.colorBgLayout,
      }}
      topActions={<MainSideBarTopActions />}
    />
  );
});

MainSideBar.displayName = 'MainSideBar';

export default MainSideBar;

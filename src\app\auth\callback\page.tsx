'use client';

import React, { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { App } from 'antd';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setUser, setLoading } = useAuthStore();
  const { message } = App.useApp();

  useEffect(() => {
    const handleCallback = async () => {
      setLoading(true);

      try {
        const token = searchParams.get('token');
        const userStr = searchParams.get('user');

        if (token && userStr) {
          const user = JSON.parse(userStr);

          // 更新认证状态
          setUser({
            ...user,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

          // 保存登录状态到localStorage
          localStorage.setItem('auth_token', token);
          localStorage.setItem('auth_user', JSON.stringify(user));
          localStorage.setItem('auth_remember_me', 'true'); // 第三方登录默认记住
          localStorage.setItem('auth_login_time', new Date().toISOString());

          message.success(`欢迎回来，${user.username}！`);
          router.push('/chat');
        } else {
          message.error('登录失败，请重试');
          router.push('/auth');
        }
      } catch (error) {
        console.error('处理登录回调失败:', error);
        message.error('登录失败，请重试');
        router.push('/auth');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [searchParams, router, setUser, setLoading, message]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">正在处理登录信息...</p>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}

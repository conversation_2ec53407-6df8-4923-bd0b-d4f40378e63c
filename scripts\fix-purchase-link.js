#!/usr/bin/env node

/**
 * 修复数据库：添加 purchase_link 字段
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'story_ai',
  charset: 'utf8mb4'
};

async function fixPurchaseLinkColumn() {
  let connection;
  
  try {
    console.log('🔌 连接数据库...');
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ 数据库连接成功');
    
    // 检查字段是否已存在
    console.log('🔍 检查 purchase_link 字段是否存在...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM information_schema.columns 
      WHERE table_schema = ? 
        AND table_name = 'agents' 
        AND column_name = 'purchase_link'
    `, [DB_CONFIG.database]);
    
    if (columns.length > 0) {
      console.log('✅ purchase_link 字段已存在，无需添加');
      return;
    }
    
    // 添加字段
    console.log('📝 添加 purchase_link 字段...');
    await connection.execute(`
      ALTER TABLE agents 
      ADD COLUMN purchase_link VARCHAR(500)
    `);
    
    console.log('✅ purchase_link 字段添加成功');
    
    // 验证字段
    console.log('🔍 验证字段添加结果...');
    const [verification] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM information_schema.columns 
      WHERE table_schema = ? 
        AND table_name = 'agents' 
        AND column_name = 'purchase_link'
    `, [DB_CONFIG.database]);
    
    if (verification.length > 0) {
      const field = verification[0];
      console.log('📊 字段信息:');
      console.log(`   名称: ${field.COLUMN_NAME}`);
      console.log(`   类型: ${field.DATA_TYPE}(${field.CHARACTER_MAXIMUM_LENGTH})`);
      console.log(`   可空: ${field.IS_NULLABLE}`);
      console.log(`   默认值: ${field.COLUMN_DEFAULT || 'NULL'}`);
      console.log('🎉 字段添加并验证成功！');
    } else {
      console.log('❌ 字段验证失败');
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log('ℹ️  字段已存在，无需重复添加');
    } else {
      throw error;
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  console.log('='.repeat(50));
  console.log('🔧 数据库修复工具 - 添加 purchase_link 字段');
  console.log('='.repeat(50));
  console.log(`📍 数据库: ${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`);
  console.log('');
  
  await fixPurchaseLinkColumn();
}

if (require.main === module) {
  main().catch(console.error);
}

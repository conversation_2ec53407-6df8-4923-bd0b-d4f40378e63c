// 智能体相关类型定义

export type AgentType = 'coze' | 'builtin';

export interface Agent {
  id: string;
  name: string;
  description?: string;
  category: AgentCategory;
  avatar?: string;
  author?: string;
  // 智能体类型
  type: AgentType;
  // 使用说明
  usageInstructions?: string;
  // 预设提示词
  presetPrompts?: string[];
  // 购买链接
  purchaseLink?: string;
  // Coze 智能体配置
  cozeConfig?: {
    apiKey: string;
    botId: string;
    userId: string;
  };
  // 内置智能体配置
  builtinConfig?: {
    secretCode: string;
    systemPrompt: string;
  };
  // 创建和更新时间
  createdAt: string;
  updatedAt: string;
  // 是否启用
  enabled: boolean;
  // 使用次数
  usageCount?: number;
  // 试用次数
  trialUsageCount?: number;
}

export type AgentCategory =
  | 'media'        // 自媒体类
  | 'novel'        // 网文小说类
  | 'film'         // 影视编导类
  | 'other';       // 其他

export interface AgentCategoryInfo {
  key: AgentCategory;
  label: string;
  icon: string;
  description: string;
}

// 智能体分类信息
export const AGENT_CATEGORIES: AgentCategoryInfo[] = [
  {
    key: 'media',
    label: '自媒体类',
    icon: 'Megaphone',
    description: '自媒体内容创作和运营'
  },
  {
    key: 'novel',
    label: '网文小说类',
    icon: 'BookOpen',
    description: '网络小说创作和编辑'
  },
  {
    key: 'film',
    label: '影视编导类',
    icon: 'Video',
    description: '影视剧本和编导辅助'
  },
  {
    key: 'other',
    label: '其他',
    icon: 'MoreHorizontal',
    description: '其他类型智能体'
  }
];

// 创建智能体的表单数据
export interface CreateAgentForm {
  name: string;
  description?: string;
  category: AgentCategory;
  avatar?: string;
  type: AgentType;
  usageInstructions?: string;
  presetPrompts?: string[];
  purchaseLink?: string;
  // Coze 智能体字段
  cozeApiKey?: string;
  cozeBotId?: string;
  cozeUserId?: string;
  // 内置智能体字段
  secretCode?: string;
  systemPrompt?: string;
  trialUsageCount?: number;
}

// 智能体定价模式
export interface AgentPricingPlan {
  id: string;
  agentId: string;
  name: string;
  type: 'per_usage' | 'time_based';
  // 按次收费字段
  usageCount?: number; // 可用次数
  pricePerUsage?: number; // 每次价格
  // 按时计费字段
  durationDays?: number; // 时长（天）
  pricePerPeriod?: number; // 每期价格
  // 通用字段
  currency: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 用户智能体订阅
export interface UserAgentSubscription {
  id: string;
  userId: string;
  agentId: string;
  pricingPlanId: string;
  // 按次收费相关
  remainingUsage: number; // 剩余使用次数
  totalUsage: number; // 总使用次数
  // 按时计费相关
  startDate?: string; // 开始时间
  endDate?: string; // 结束时间
  // 通用字段
  status: 'active' | 'expired' | 'cancelled';
  purchasePrice: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

// 智能体使用记录
export interface AgentUsageLog {
  id: string;
  userId: string;
  agentId: string;
  subscriptionId?: string;
  sessionId?: string;
  usageType: 'chat' | 'generation';
  tokensUsed: number;
  cost: number;
  usageCount: number; // 本次使用次数
  expiryDate?: string; // 截止时间（用于定时定价模式）
  createdAt: string;
}

// 智能体状态
export interface AgentState {
  agents: Agent[];
  selectedCategory: AgentCategory | 'all';
  searchQuery: string;
  loading: boolean;
  error: string | null;
}

// 智能体操作
export interface AgentActions {
  // 获取智能体列表
  fetchAgents: () => Promise<void>;
  // 添加智能体
  addAgent: (agent: CreateAgentForm) => Promise<Agent>;
  // 更新智能体
  updateAgent: (id: string, updates: Partial<Agent>) => Promise<void>;
  // 删除智能体
  deleteAgent: (id: string) => Promise<void>;
  // 设置选中的分类
  setSelectedCategory: (category: AgentCategory | 'all') => void;
  // 设置搜索查询
  setSearchQuery: (query: string) => void;
  // 添加智能体到会话
  addAgentToSession: (agentId: string) => Promise<string>;
  // 增加使用次数
  incrementUsageCount: (agentId: string) => Promise<void>;
}

export type AgentStore = AgentState & AgentActions;

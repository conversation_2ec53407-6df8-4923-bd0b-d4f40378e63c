/** @type {import('next').NextConfig} */
const nextConfig = {
  // Vercel 部署时不需要 standalone 输出
  // output: 'standalone',

  experimental: {
    optimizePackageImports: [
      'antd',
      '@lobehub/ui',
      'lucide-react',
      'framer-motion'
    ]
  },

  // 外部包配置
  serverExternalPackages: ['mysql2'],
  transpilePackages: ['antd-style'],
  webpack: (config) => {
    // 支持 WebAssembly
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      layers: true,
    };

    // 处理 canvas 依赖
    config.resolve.alias.canvas = false;

    // 抑制 antd 兼容性警告
    config.stats = {
      ...config.stats,
      warnings: false,
      warningsFilter: [
        /antd.*compatible/i,
        /antd v5 support React is 16/i,
        /https:\/\/u\.ant\.design\/v5-for-19/i,
      ],
    };

    // 抑制 webpack 警告
    config.ignoreWarnings = [
      /antd.*compatible/i,
      /antd v5 support React is 16/i,
      /https:\/\/u\.ant\.design\/v5-for-19/i,
    ];

    return config;
  },
  // 静态资源优化
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // 压缩配置
  compress: true,

  // 开发模式配置
  reactStrictMode: true,

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/',
        destination: '/chat',
        permanent: false,
      },
    ];
  },

  // 安全头配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
}

module.exports = nextConfig
import { useState, useMemo } from 'react';
import { ChatSession } from '@/types';

export interface SessionGroup {
  title: string;
  sessions: ChatSession[];
}

// 时间分组函数
const groupSessionsByTime = (sessions: ChatSession[]): SessionGroup[] => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  const groups: SessionGroup[] = [
    { title: '今天', sessions: [] },
    { title: '昨天', sessions: [] },
    { title: '本周', sessions: [] },
    { title: '更早', sessions: [] },
  ];

  sessions.forEach((session) => {
    const sessionDate = new Date(session.updatedAt);

    if (sessionDate >= today) {
      groups[0].sessions.push(session);
    } else if (sessionDate >= yesterday) {
      groups[1].sessions.push(session);
    } else if (sessionDate >= weekAgo) {
      groups[2].sessions.push(session);
    } else {
      groups[3].sessions.push(session);
    }
  });

  // 过滤掉空的分组
  return groups.filter(group => group.sessions.length > 0);
};

export const useSessionSearch = (sessions: ChatSession[]) => {
  const [searchKeyword, setSearchKeyword] = useState('');

  const filteredSessions = useMemo(() => {
    if (!searchKeyword.trim()) {
      return sessions;
    }

    const keyword = searchKeyword.toLowerCase().trim();

    return sessions.filter((session) => {
      // 搜索会话标题
      if (session.title?.toLowerCase().includes(keyword)) {
        return true;
      }

      // 搜索消息内容
      if (session.messages?.some(message =>
        message.content.toLowerCase().includes(keyword)
      )) {
        return true;
      }

      return false;
    });
  }, [sessions, searchKeyword]);

  const groupedSessions = useMemo(() => {
    return groupSessionsByTime(filteredSessions);
  }, [filteredSessions]);

  return {
    searchKeyword,
    setSearchKeyword,
    filteredSessions,
    groupedSessions,
  };
};

-- 为智能体表添加试用次数字段
-- 这个字段用于设置用户在没有付费订阅的情况下可以免费试用该智能体的次数

-- 1. 添加试用次数字段
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS trial_usage_count INT DEFAULT 3 COMMENT '试用次数，用户可以免费试用的次数';

-- 2. 添加索引以提高查询性能
ALTER TABLE agents 
ADD INDEX IF NOT EXISTS idx_trial_usage_count (trial_usage_count);

-- 3. 更新现有智能体的试用次数（如果为 NULL，设置为默认值 3）
UPDATE agents 
SET trial_usage_count = 3 
WHERE trial_usage_count IS NULL;

-- 4. 为不同类型的智能体设置不同的默认试用次数
-- 通用助手：5次试用
UPDATE agents 
SET trial_usage_count = 5 
WHERE category = 'general' AND trial_usage_count = 3;

-- 编程助手：3次试用（代码生成成本较高）
UPDATE agents 
SET trial_usage_count = 3 
WHERE category = 'programming' AND trial_usage_count = 3;

-- 写作助手：4次试用
UPDATE agents 
SET trial_usage_count = 4 
WHERE category = 'writing' AND trial_usage_count = 3;

-- 教育助手：6次试用（鼓励学习）
UPDATE agents 
SET trial_usage_count = 6 
WHERE category = 'education' AND trial_usage_count = 3;

-- 商务助手：2次试用（商业用途，鼓励付费）
UPDATE agents 
SET trial_usage_count = 2 
WHERE category = 'business' AND trial_usage_count = 3;

-- 创意助手：4次试用
UPDATE agents 
SET trial_usage_count = 4 
WHERE category = 'creative' AND trial_usage_count = 3;

-- 分析助手：3次试用
UPDATE agents 
SET trial_usage_count = 3 
WHERE category = 'analysis' AND trial_usage_count = 3;

-- 翻译助手：5次试用
UPDATE agents 
SET trial_usage_count = 5 
WHERE category = 'translation' AND trial_usage_count = 3;

'use client';

import { createStyles } from 'antd-style';
import { memo, useState } from 'react';
import { Flexbox } from 'react-layout-kit';
import { MessageSquare, MoreHorizontal, Trash2, Edit3 } from 'lucide-react';
import { ActionIcon } from '@lobehub/ui';
import { Dropdown, Input, App } from 'antd';
import type { MenuProps } from 'antd';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import { ChatSession } from '@/types';
import { SessionGroup } from '@/hooks/useSessionSearch';

// 时间格式化函数
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    return '刚刚';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}小时前`;
  } else if (diffInHours < 24 * 7) {
    return `${Math.floor(diffInHours / 24)}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }
};

const useStyles = createStyles(({ css, token }) => ({
  sessionItem: css`
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 2px;
    
    &:hover {
      background: ${token.colorFillQuaternary};
      
      .session-actions {
        opacity: 1;
      }
    }
    
    &.active {
      background: ${token.colorPrimary}15;
      border: 1px solid ${token.colorPrimary}30;
    }
  `,
  
  sessionIcon: css`
    color: ${token.colorTextTertiary};
    margin-right: 8px;
    flex-shrink: 0;
  `,
  
  sessionContent: css`
    flex: 1;
    min-width: 0;
  `,
  
  sessionTitle: css`
    font-size: 14px;
    font-weight: 500;
    color: ${token.colorText};
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,
  
  sessionPreview: css`
    font-size: 12px;
    color: ${token.colorTextTertiary};
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,

  sessionTime: css`
    font-size: 11px;
    color: ${token.colorTextQuaternary};
    margin-top: 2px;
  `,
  
  sessionActions: css`
    opacity: 0;
    transition: opacity 0.2s ease;
    margin-left: 8px;
  `,
  
  emptyState: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: ${token.colorTextTertiary};
  `,
  
  emptyIcon: css`
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  `,
  
  emptyText: css`
    font-size: 14px;
    line-height: 1.5;
  `,

  groupTitle: css`
    font-size: 12px;
    font-weight: 600;
    color: ${token.colorTextTertiary};
    padding: 8px 12px 4px 12px;
    margin-top: 8px;

    &:first-child {
      margin-top: 0;
    }
  `,
}));

interface SessionListProps {
  sessions: ChatSession[];
  groupedSessions?: SessionGroup[];
  showGrouped?: boolean;
  isHistoryList?: boolean; // 新增：标识是否为历史会话列表
}

const SessionList = memo<SessionListProps>(({ sessions, groupedSessions, showGrouped = false, isHistoryList = false }) => {
  const { styles } = useStyles();
  const { currentSessions, activeSessionId, switchToSession, deleteSession, updateSessionTitle } = useChatStore();

  // 获取当前活跃会话
  const currentSession = currentSessions.find(s => s.id === activeSessionId);
  const [editingSessionUniqueId, setEditingSessionUniqueId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const { modal, message } = App.useApp();

  const handleSessionClick = (session: ChatSession) => {
    if (isHistoryList) {
      // 历史会话：使用 switchToSession 进行对调
      switchToSession(session.sessionUniqueId);
    } else {
      // 当前会话：直接设置活跃会话ID
      useChatStore.setState({ activeSessionId: session.id });
    }
  };

  const handleDeleteSession = (sessionUniqueId: string) => {
    modal.confirm({
      title: '删除会话',
      content: '确定要删除这个会话吗？此操作无法撤销。',
      okText: '删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        deleteSession(sessionUniqueId);
        message.success('会话已删除');
      },
    });
  };

  const handleEditSession = (sessionUniqueId: string, currentTitle: string) => {
    setEditingSessionUniqueId(sessionUniqueId);
    setEditingTitle(currentTitle);
  };

  const handleSaveTitle = () => {
    if (editingSessionUniqueId && editingTitle.trim()) {
      updateSessionTitle(editingSessionUniqueId, editingTitle.trim());
      setEditingSessionUniqueId(null);
      setEditingTitle('');
      message.success('会话标题已更新');
    }
  };

  const handleCancelEdit = () => {
    setEditingSessionUniqueId(null);
    setEditingTitle('');
  };

  // 获取会话显示标题
  const getSessionDisplayTitle = (session: ChatSession): string => {
    // 如果是智能体会话且有智能体名称，优先显示智能体名称
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID && session.agentName) {
      return session.agentName;
    }

    // 如果用户已经自定义了标题，显示自定义标题
    if (session.title && session.title !== '新会话' && session.title !== '新对话') {
      return session.title;
    }

    // 否则显示默认标题
    return session.title || '新会话';
  };

  const getSessionMenuItems = (session: ChatSession): MenuProps['items'] => [
    {
      key: 'edit',
      label: '重命名',
      icon: <Edit3 size={14} />,
      onClick: () => handleEditSession(session.sessionUniqueId, getSessionDisplayTitle(session)),
    },
    {
      key: 'delete',
      label: '删除',
      icon: <Trash2 size={14} />,
      danger: true,
      onClick: () => handleDeleteSession(session.sessionUniqueId),
    },
  ];

  // 渲染单个会话项
  const renderSessionItem = (session: ChatSession) => (
    <div
      key={session.id}
      className={`${styles.sessionItem} ${currentSession?.id === session.id ? 'active' : ''}`}
      onClick={() => handleSessionClick(session)}
    >
      <MessageSquare size={16} className={styles.sessionIcon} />
      <div className={styles.sessionContent}>
        {editingSessionUniqueId === session.sessionUniqueId ? (
          <Input
            value={editingTitle}
            onChange={(e) => setEditingTitle(e.target.value)}
            onPressEnter={handleSaveTitle}
            onBlur={handleSaveTitle}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                handleCancelEdit();
              }
              e.stopPropagation();
            }}
            onClick={(e) => e.stopPropagation()}
            autoFocus
            size="small"
            style={{ fontSize: '14px', fontWeight: 500 }}
          />
        ) : (
          <>
            <div className={styles.sessionTitle}>
              {getSessionDisplayTitle(session)}
            </div>
            <div className={styles.sessionPreview}>
              {session.messages && session.messages.length > 0
                ? session.messages[session.messages.length - 1].content.slice(0, 30) + '...'
                : '开始新的对话'}
            </div>
            <div className={styles.sessionTime}>
              {formatTime(session.updatedAt)}
            </div>
          </>
        )}
      </div>
      <div className={`session-actions ${styles.sessionActions}`}>
        <Dropdown
          menu={{ items: getSessionMenuItems(session) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <ActionIcon
            icon={MoreHorizontal}
            size={{ blockSize: 24, size: 14 }}
            onClick={(e) => e.stopPropagation()}
          />
        </Dropdown>
      </div>
    </div>
  );

  if (!sessions || sessions.length === 0) {
    return (
      <div className={styles.emptyState}>
        <MessageSquare className={styles.emptyIcon} />
        <div className={styles.emptyText}>
          还没有会话<br />
          点击上方的 + 按钮开始新的对话
        </div>
      </div>
    );
  }

  // 如果显示分组且有分组数据
  if (showGrouped && groupedSessions && groupedSessions.length > 0) {
    return (
      <Flexbox gap={0}>
        {groupedSessions.map((group) => (
          <div key={group.title}>
            <div className={styles.groupTitle}>{group.title}</div>
            {group.sessions.map(renderSessionItem)}
          </div>
        ))}
      </Flexbox>
    );
  }

  // 默认显示（不分组）
  return (
    <Flexbox gap={0}>
      {sessions.map(renderSessionItem)}
    </Flexbox>
  );

});

export default SessionList;

import { createGlobalStyle } from 'antd-style';

export const GlobalStyle = createGlobalStyle`
  html,
  body,
  #__next {
    position: relative;
    overscroll-behavior: none;
    height: 100%;
    min-height: 100dvh;
    max-height: 100dvh;
    background: ${({ theme }) => theme.colorBgLayout};

    @media (min-device-width: 576px) {
      overflow: hidden;
    }
  }

  body {
    /* 提高合成层级，强制硬件加速，否则会有渲染黑边出现 */
    will-change: opacity;
    transform: translateZ(0);
  }

  * {
    scrollbar-color: ${({ theme }) => theme.colorFill} transparent;
    scrollbar-width: thin;

    ::-webkit-scrollbar {
      width: 0.75em;
      height: 0.75em;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
    }

    :hover::-webkit-scrollbar-thumb {
      border: 3px solid transparent;
      background-color: ${({ theme }) => theme.colorText};
      background-clip: content-box;
    }

    ::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  /* Ant Design 组件样式覆盖 */
  .ant-btn {
    border-radius: ${({ theme }) => theme.borderRadius}px;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-input,
  .ant-input-affix-wrapper {
    border-radius: ${({ theme }) => theme.borderRadius}px;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    
    &:focus,
    &:focus-within {
      box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }
  }

  .ant-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
  }

  .ant-modal {
    border-radius: 12px;
    
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .ant-drawer {
    .ant-drawer-content {
      background: ${({ theme }) => theme.colorBgContainer};
    }
  }

  /* 自定义动画 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .fade-in-up {
    animation: fadeInUp 0.3s ease-out;
  }

  .slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  /* 玻璃效果 */
  .glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
  }

  /* 渐变文字 */
  .text-gradient {
    background: linear-gradient(135deg, ${({ theme }) => theme.colorPrimary}, #34a853);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 加载状态 */
  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

export * from './theme';
-- 修复数据库：添加 purchase_link 字段
-- 如果字段已存在，此语句会被忽略

-- 检查并添加 purchase_link 字段
SET @sql = (
  SELECT IF(
    COUNT(*) = 0,
    'ALTER TABLE agents ADD COLUMN purchase_link VARCHAR(500)',
    'SELECT "purchase_link column already exists" as message'
  )
  FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
    AND table_name = 'agents' 
    AND column_name = 'purchase_link'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'agents' 
  AND column_name = 'purchase_link';

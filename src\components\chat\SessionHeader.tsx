'use client';

import { ActionIcon } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { MessageSquarePlus, Search } from 'lucide-react';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';
import { Input } from 'antd';
import { useChatStore } from '@/store/chat';
const DESKTOP_HEADER_ICON_SIZE = { blockSize: 36, size: 22 };

export const useStyles = createStyles(({ css, token }) => ({
  logo: css`
    color: ${token.colorText};
    fill: ${token.colorText};
    font-size: 18px;
    font-weight: 600;
  `,
  top: css`
    position: sticky;
    inset-block-start: 0;
    padding-block-start: 10px;
  `,
  searchInput: css`
    border-radius: 8px;
    background: ${token.colorFillQuaternary};
    border: 1px solid ${token.colorBorderSecondary};
    
    .ant-input {
      background: transparent;
      border: none;
      box-shadow: none;
      
      &:focus {
        box-shadow: none;
      }
    }
    
    &:hover {
      border-color: ${token.colorPrimary};
    }
    
    &:focus-within {
      border-color: ${token.colorPrimary};
      box-shadow: 0 0 0 2px ${token.colorPrimary}20;
    }
  `,
}));

interface SessionHeaderProps {
  searchKeyword: string;
  onSearchChange: (value: string) => void;
}

const SessionHeader = memo<SessionHeaderProps>(({ searchKeyword, onSearchChange }) => {
  const { styles } = useStyles();
  const { createSession } = useChatStore();

  const handleCreateSession = () => {
    createSession();
  };

  return (
    <Flexbox className={styles.top} gap={16} paddingInline={8}>
      <Flexbox align={'flex-start'} horizontal justify={'space-between'}>
        <Flexbox
          align={'center'}
          gap={4}
          horizontal
          style={{
            paddingInlineStart: 4,
            paddingTop: 2,
          }}
        >
          <div className={styles.logo}>Gemini AI</div>
        </Flexbox>
        <Flexbox align={'center'} gap={4} horizontal>
          <ActionIcon
            icon={MessageSquarePlus}
            onClick={handleCreateSession}
            size={DESKTOP_HEADER_ICON_SIZE}
            style={{ flex: 'none' }}
            title="新建会话"
          />
        </Flexbox>
      </Flexbox>
      <Input
        className={styles.searchInput}
        placeholder="搜索会话..."
        prefix={<Search size={16} />}
        size="middle"
        value={searchKeyword}
        onChange={(e) => onSearchChange(e.target.value)}
        allowClear
      />
    </Flexbox>
  );
});

export default SessionHeader;

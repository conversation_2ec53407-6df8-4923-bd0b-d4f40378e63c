'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { createStyles } from 'antd-style';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Button
} from 'antd';
import {
  Settings,
  ArrowLeft,
  Layers,
  Bot
} from 'lucide-react';
import { useAuthStore } from '@/store/auth';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { Flexbox } from 'react-layout-kit';
import SimpleBatchGenerator from '@/components/batch/SimpleBatchGenerator';
import { ProviderConfigComponent } from '@/components/ProviderConfig';
import MainSideBar from '@/components/layout/MainSideBar';
import AgentManagement from '@/components/agents/AgentManagement';
import PermissionGuard from '@/components/auth/PermissionGuard';
import { Permission } from '@/hooks/usePermissions';

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;
  `,
  container: css`
    flex: 1;
    display: flex;
    background: ${token.colorBgContainer};
  `,

  sidebar: css`
    width: 280px;
    padding: 16px 12px;
    border-right: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgLayout};
    flex: none;
  `,

  backButton: css`
    margin-bottom: 16px;

    .ant-btn {
      color: ${token.colorTextSecondary};

      &:hover {
        color: ${token.colorPrimary};
        background: ${token.colorPrimaryBg};
      }
    }
  `,

  content: css`
    flex: 1;
    padding: 24px;
    overflow-y: auto;
  `,

  sidebarTitle: css`
    margin-bottom: 20px;

    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
      font-size: 16px;
      font-weight: 600;
      color: ${token.colorText};
    }

    .description {
      color: ${token.colorTextSecondary};
      font-size: 14px;
    }
  `,

  menuItem: css`
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 4px;
    color: ${token.colorText};

    &:hover {
      background: ${token.colorFillTertiary};
    }

    &.active {
      background: ${token.colorPrimaryBg};
      color: ${token.colorPrimary};
    }

    .icon {
      width: 16px;
      height: 16px;
    }

    .label {
      font-size: 14px;
    }
  `,

  formSection: css`
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 600;
      color: ${token.colorText};
    }

    .section-description {
      color: ${token.colorTextSecondary};
      margin-bottom: 24px;
      font-size: 14px;
    }
  `,

  actionButtons: css`
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid ${token.colorBorderSecondary};
    margin-top: 32px;
  `,

  providersGrid: css`
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  `,
}));



type SettingsTab = 'ai-providers' | 'batch' | 'agents';

const SettingsContent: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { checkLoginStatus, user } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState<SettingsTab>('agents');








  // 初始化组件
  useEffect(() => {
    const init = async () => {
      // 确保认证状态已经恢复（由PermissionGuard处理）
      await checkLoginStatus();
      setMounted(true);
    };

    init();
  }, [checkLoginStatus]);



  // 处理URL参数
  useEffect(() => {
    const tab = searchParams.get('tab') as SettingsTab;
    if (tab && ['ai-providers', 'batch', 'agents'].includes(tab)) {
      setActiveTab(tab);
    } else if (!tab && mounted) {
      // 如果没有tab参数，重定向到默认页面
      router.replace('/settings?tab=agents', { scroll: false });
    }
  }, [searchParams, mounted, router]);





  const handleTabChange = (tab: SettingsTab) => {
    setActiveTab(tab);
    router.push(`/settings?tab=${tab}`, { scroll: false });
  };

  const allMenuItems = [
    {
      key: 'ai-providers',
      icon: <Bot size={16} />,
      label: 'AI 模型配置',
      onClick: () => handleTabChange('ai-providers'),
    },
    // 隐藏提示词管理界面 - 任何人都无法看到该页面
    // {
    //   key: 'prompts',
    //   icon: <MessageSquare size={16} />,
    //   label: '提示词管理',
    //   onClick: () => handleTabChange('prompts'),
    //   requiresPermission: true,
    // },
    {
      key: 'batch',
      icon: <Layers size={16} />,
      label: '批量生成',
      onClick: () => handleTabChange('batch'),
    },

    {
      key: 'agents',
      icon: <Bot size={16} />,
      label: '智能体管理',
      onClick: () => handleTabChange('agents'),
    },
  ];

  // 根据权限过滤菜单项（现在所有菜单项都没有权限要求）
  const menuItems = allMenuItems;

  if (!mounted) {
    return <LoadingSpinner />;
  }

  const renderContent = () => {
    switch (activeTab) {

      case 'ai-providers':
        return (
          <div className={styles.formSection}>
            <div className="section-title">
              <Bot size={18} />
              AI 模型配置
            </div>
            <div className="section-description">
              配置多个AI提供商（ChatGPT、Claude、Gemini、DeepSeek）的API密钥和模型参数
            </div>

            <div className={styles.providersGrid}>
              <ProviderConfigComponent provider="openai" />
              <ProviderConfigComponent provider="anthropic" />
              <ProviderConfigComponent provider="google" />
              <ProviderConfigComponent provider="deepseek" />
            </div>
          </div>
        );

      case 'batch':
        return <SimpleBatchGenerator />;



      case 'agents':
        return <AgentManagement />;

      default:
        return null;
    }
  };

  return (
    <div className={styles.layout}>
      {/* 最左边的主导航侧边栏 */}
      <MainSideBar />

      <Flexbox horizontal className={styles.container}>
        {/* 左侧边栏 */}
        <div className={styles.sidebar}>
          {/* 返回按钮 */}
          <div className={styles.backButton}>
            <Button
              type="text"
              icon={<ArrowLeft size={16} />}
              onClick={() => router.push('/chat')}
            >
              返回
            </Button>
          </div>

          <div className={styles.sidebarTitle}>
            <div className="title">
              <Settings size={16} />
              设置
            </div>
            <div className="description">
              管理应用配置和偏好设置
            </div>
          </div>

          {/* 菜单项 */}
          {menuItems.map((item) => (
            <div
              key={item.key}
              className={`${styles.menuItem} ${activeTab === item.key ? 'active' : ''}`}
              onClick={item.onClick}
            >
              <div className="icon">{item.icon}</div>
              <div className="label">{item.label}</div>
            </div>
          ))}
        </div>

        {/* 右侧内容区域 */}
        <div className={styles.content}>
          {renderContent()}
        </div>
      </Flexbox>
    </div>
  );
};

const SettingsPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载设置...</p>
        </div>
      </div>
    }>
      <SettingsContent />
    </Suspense>
  );
};

// 包装设置页面，添加权限检查
const ProtectedSettingsPage = () => {
  return (
    <PermissionGuard permission={Permission.VIEW_SETTINGS}>
      <SettingsPage />
    </PermissionGuard>
  );
};

export default ProtectedSettingsPage;
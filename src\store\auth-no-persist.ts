import { create } from 'zustand';
import { AuthState, User, LoginCredentials } from '@/types';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  checkLoginStatus: () => Promise<void>;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 模拟的管理员凭据
const ADMIN_CREDENTIALS = [
  {
    username: 'wkhgogogo',
    password: 'Wkh11072975813'
  },
  {
    username: 'weitian',
    password: 'Aa123456'
  }
];

export const useAuthStoreNoPersist = create<AuthStore>((set, get) => ({
  // Initial state
  isLoggedIn: false,
  user: null,
  token: null,
  loading: false,
  error: null,

  // Actions
  login: async (credentials: LoginCredentials) => {
    set({ loading: true, error: null });

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 验证凭据
      const isValid = ADMIN_CREDENTIALS.some(
        admin => admin.username === credentials.username && admin.password === credentials.password
      );

      if (isValid) {
        const user: User = {
          id: '1',
          username: credentials.username,
          email: `${credentials.username}@example.com`,
          avatar: '',
          role: 'admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
        };

        const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        set({
          isLoggedIn: true,
          user,
          token,
          loading: false,
          error: null,
        });

        // 保存到 localStorage（手动管理）
        if (credentials.rememberMe) {
          localStorage.setItem('gemini_user', JSON.stringify(user));
          localStorage.setItem('gemini_token', token);
          localStorage.setItem('gemini_remember_me', 'true');
          localStorage.setItem('gemini_login_time', new Date().toISOString());
        }

        return true;
      } else {
        set({
          loading: false,
          error: '用户名或密码错误',
        });
        return false;
      }
    } catch (error) {
      set({
        loading: false,
        error: '登录失败，请稍后重试',
      });
      return false;
    }
  },

  logout: () => {
    set({
      isLoggedIn: false,
      user: null,
      token: null,
      loading: false,
      error: null,
    });

    // 清除本地存储
    localStorage.removeItem('gemini_user');
    localStorage.removeItem('gemini_token');
    localStorage.removeItem('gemini_remember_me');
    localStorage.removeItem('gemini_login_time');
  },

  checkLoginStatus: async () => {
    const rememberMe = localStorage.getItem('gemini_remember_me') === 'true';
    const loginTime = localStorage.getItem('gemini_login_time');

    if (rememberMe && loginTime) {
      const loginDate = new Date(loginTime);
      const now = new Date();
      const daysDiff = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60 * 24);

      // 如果在7天内，保持登录状态
      if (daysDiff <= 7) {
        const user = localStorage.getItem('gemini_user');
        const token = localStorage.getItem('gemini_token');
        if (user && token) {
          set({ 
            isLoggedIn: true,
            user: JSON.parse(user),
            token: token
          });
          return;
        }
      } else {
        // 超过7天，清除记住状态
        localStorage.removeItem('gemini_remember_me');
        localStorage.removeItem('gemini_login_time');
      }
    }

    set({ isLoggedIn: false });
  },

  setUser: (user: User | null) => {
    set({ user });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));

'use client';

import { ThemeProvider } from '@lobehub/ui';
import { ThemeAppearance } from 'antd-style';
import { ReactNode, memo, useEffect, useState } from 'react';

// Lobe Chat 主题常量
export const LOBE_THEME_APPEARANCE = 'LOBE_THEME_APPEARANCE';
export const LOBE_THEME_PRIMARY_COLOR = 'LOBE_THEME_PRIMARY_COLOR';
export const LOBE_THEME_NEUTRAL_COLOR = 'LOBE_THEME_NEUTRAL_COLOR';

interface LobeThemeProviderProps {
  children: ReactNode;
  defaultAppearance?: ThemeAppearance;
  defaultPrimaryColor?: string;
  defaultNeutralColor?: string;
}

const LobeThemeProvider = memo<LobeThemeProviderProps>(({
  children,
  defaultAppearance = 'light',
  defaultPrimaryColor = '',
  defaultNeutralColor = '',
}) => {
  const [appearance, setAppearance] = useState<ThemeAppearance>(defaultAppearance);
  const [primaryColor, setPrimaryColor] = useState(defaultPrimaryColor);
  const [neutralColor, setNeutralColor] = useState(defaultNeutralColor);

  // 从 localStorage 读取主题设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedAppearance = localStorage.getItem(LOBE_THEME_APPEARANCE) as ThemeAppearance;
      const savedPrimaryColor = localStorage.getItem(LOBE_THEME_PRIMARY_COLOR);
      const savedNeutralColor = localStorage.getItem(LOBE_THEME_NEUTRAL_COLOR);

      if (savedAppearance) setAppearance(savedAppearance);
      if (savedPrimaryColor) setPrimaryColor(savedPrimaryColor);
      if (savedNeutralColor) setNeutralColor(savedNeutralColor);
    }
  }, []);

  // 保存主题设置到 localStorage
  const handleAppearanceChange = (newAppearance: ThemeAppearance) => {
    setAppearance(newAppearance);
    if (typeof window !== 'undefined') {
      localStorage.setItem(LOBE_THEME_APPEARANCE, newAppearance);
    }
  };

  const handlePrimaryColorChange = (newColor: string) => {
    setPrimaryColor(newColor);
    if (typeof window !== 'undefined') {
      localStorage.setItem(LOBE_THEME_PRIMARY_COLOR, newColor);
    }
  };

  const handleNeutralColorChange = (newColor: string) => {
    setNeutralColor(newColor);
    if (typeof window !== 'undefined') {
      localStorage.setItem(LOBE_THEME_NEUTRAL_COLOR, newColor);
    }
  };

  return (
    <ThemeProvider
      appearance={appearance}
      customTheme={{
        neutralColor: (neutralColor && ['olive', 'mauve', 'sage', 'sand', 'slate'].includes(neutralColor) ? neutralColor : defaultNeutralColor) as "olive" | "mauve" | "sage" | "sand" | "slate" | undefined,
        primaryColor: (primaryColor && ['blue', 'cyan', 'green', 'magenta', 'red', 'yellow', 'purple', 'orange', 'volcano', 'geekblue', 'lime', 'gold'].includes(primaryColor) ? primaryColor : defaultPrimaryColor) as "blue" | "cyan" | "green" | "magenta" | "red" | "yellow" | "purple" | "orange" | "volcano" | "geekblue" | "lime" | "gold" | undefined,
      }}
      defaultAppearance={defaultAppearance}
      onAppearanceChange={handleAppearanceChange}
      theme={{
        cssVar: true,
        token: {
          fontFamily: 'Google Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
          colorPrimary: '#1a73e8',
          colorLink: '#1a73e8',
          colorLinkHover: '#1557b0',
          colorLinkActive: '#1557b0',
        },
        components: {
          Button: {
            borderRadius: 8,
            linkHoverBg: 'transparent',
            colorLink: '#1a73e8',
            colorLinkHover: '#1557b0',
            colorLinkActive: '#1557b0',
          },
          Input: {
            borderRadius: 8,
          },
          Card: {
            borderRadius: 12,
          },
        },
      }}
      themeMode={appearance as any}
    >
      {children}
    </ThemeProvider>
  );
});

LobeThemeProvider.displayName = 'LobeThemeProvider';

export default LobeThemeProvider;

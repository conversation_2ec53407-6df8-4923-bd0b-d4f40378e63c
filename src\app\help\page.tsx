'use client';

import React from 'react';
import { createStyles } from 'antd-style';
import { <PERSON><PERSON>, Card, Typography, Divider, Space, Tag } from 'antd';
import { ArrowLeft, MessageSquare, Bot, Mail, HelpCircle, BookOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';
import MainSideBar from '@/components/layout/MainSideBar';

const { Title, Paragraph, Text } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;
  `,
  container: css`
    flex: 1;
    height: 100vh;
    background: ${token.colorBgLayout};
    padding: 24px;
    overflow-y: auto;
  `,

  content: css`
    background: ${token.colorBgContainer};
    border-radius: 8px;
    padding: 24px;
    margin: 0 auto;
    max-width: 1000px;
  `,

  contentInner: css`
    max-width: 800px;
    margin: 0 auto;
  `,

  backButton: css`
    margin-bottom: 24px;
  `,

  section: css`
    margin-bottom: 32px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      color: ${token.colorText};
      font-size: 18px;
      font-weight: 600;
    }
  `,

  featureCard: css`
    margin-bottom: 16px;

    .feature-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .feature-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: ${token.colorFillSecondary};
        display: flex;
        align-items: center;
        justify-content: center;
        color: ${token.colorText};
      }

      .feature-title {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
      }
    }

    .feature-description {
      color: ${token.colorTextSecondary};
      margin-bottom: 12px;
    }

    .feature-steps {
      background: ${token.colorFillAlter};
      border-radius: 6px;
      padding: 12px;

      ol {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
          color: ${token.colorTextSecondary};
        }
      }
    }
  `,

  contactCard: css`
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorder};

    .contact-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  `,

  versionInfo: css`
    text-align: center;
    padding: 16px;
    background: ${token.colorFillAlter};
    border-radius: 6px;

    .version-tag {
      margin-bottom: 8px;
    }
  `,
}));

const HelpPage: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();

  const features = [
    {
      icon: <MessageSquare size={18} />,
      title: '智能对话',
      description: '与AI进行自然语言对话，获得智能回答和建议',
      steps: [
        '点击左侧导航栏的"智能对话"图标',
        '在输入框中输入您的问题或需求',
        '按Enter键或点击发送按钮',
        '等待AI生成回复，支持多轮对话'
      ]
    },
    {
      icon: <Bot size={18} />,
      title: '智能体',
      description: '使用专业的AI智能体来处理特定领域的任务',
      steps: [
        '点击左侧导航栏的"智能体"图标',
        '浏览不同类别的智能体',
        '点击感兴趣的智能体查看详情',
        '点击"开始对话"与智能体交互'
      ]
    }
  ];

  return (
    <div className={styles.layout}>
      {/* 最左边的主导航侧边栏 */}
      <MainSideBar />

      <div className={styles.container}>
        {/* 主内容区域 */}
        <div className={styles.content}>
          <div className={styles.contentInner}>
            {/* 返回按钮 */}
            <div className={styles.backButton}>
              <Button
                type="text"
                icon={<ArrowLeft size={16} />}
                onClick={() => router.back()}
              >
                返回
              </Button>
            </div>

            {/* 欢迎信息 */}
            <div style={{ marginBottom: 32 }}>
              <Title level={2}>欢迎使用智能AI助手平台</Title>
              <Paragraph type="secondary">
                这里是您的使用指南，帮助您快速上手并充分利用平台的各项功能。
              </Paragraph>
            </div>

            {/* 功能介绍 */}
            <div className={styles.section}>
              <div className="section-title">
                <BookOpen size={18} />
                主要功能
              </div>

              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {features.map((feature, index) => (
                  <Card key={index} className={styles.featureCard}>
                    <div className="feature-header">
                      <div className="feature-icon">
                        {feature.icon}
                      </div>
                      <Title level={4} className="feature-title">
                        {feature.title}
                      </Title>
                    </div>

                    <Paragraph className="feature-description">
                      {feature.description}
                    </Paragraph>

                    <div className="feature-steps">
                      <Text strong>使用步骤：</Text>
                      <ol>
                        {feature.steps.map((step, stepIndex) => (
                          <li key={stepIndex}>{step}</li>
                        ))}
                      </ol>
                    </div>
                  </Card>
                ))}
              </Space>
            </div>

            <Divider />

            {/* 常见问题 */}
            <div className={styles.section}>
              <div className="section-title">
                <HelpCircle size={18} />
                常见问题
              </div>

              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Card>
                  <Title level={4}>智能体是什么？</Title>
                  <Paragraph>
                    智能体是针对特定领域或任务训练的专业AI助手。例如编程助手、写作助手、翻译助手等。
                    每个智能体都有专门的知识和技能，能够更好地处理相关任务。
                  </Paragraph>
                </Card>

                <Card>
                  <Title level={4}>如何开始对话？</Title>
                  <Paragraph>
                    点击左侧导航栏的"智能对话"图标，在输入框中输入您的问题，
                    按Enter键发送。您可以与AI进行多轮对话，获得智能回答和建议。
                  </Paragraph>
                </Card>

                <Card>
                  <Title level={4}>遇到问题怎么办？</Title>
                  <Paragraph>
                    如果遇到技术问题，请检查网络连接。如需进一步帮助，
                    可以通过下方的联系方式联系开发者。
                  </Paragraph>
                </Card>
              </Space>
            </div>

            <Divider />

            {/* 使用技巧 */}
            <div className={styles.section}>
              <div className="section-title">
                <MessageSquare size={18} />
                使用技巧
              </div>

              <Card>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>💡 提示词优化：</Text>
                    <Paragraph style={{ marginTop: 8 }}>
                      • 描述要清晰具体，避免模糊表达<br/>
                      • 可以指定输出格式，如"请用列表形式回答"<br/>
                      • 提供上下文信息有助于获得更准确的回答
                    </Paragraph>
                  </div>

                  <div>
                    <Text strong>🚀 效率提升：</Text>
                    <Paragraph style={{ marginTop: 8 }}>
                      • 使用智能体处理专业任务效果更好<br/>
                      • 保存常用的对话可以快速复用<br/>
                      • 合理利用多轮对话功能
                    </Paragraph>
                  </div>

                  <div>
                    <Text strong>⚠️ 注意事项：</Text>
                    <Paragraph style={{ marginTop: 8 }}>
                      • 请勿输入敏感个人信息<br/>
                      • AI回答仅供参考，重要决策请谨慎考虑<br/>
                      • 保持良好的对话习惯
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </div>

            <Divider />

            {/* 开发者信息 */}
            <div className={styles.section}>
              <div className="section-title">
                <Mail size={18} />
                开发者信息
              </div>

              <Card className={styles.contactCard}>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <Title level={3} style={{ margin: 0 }}>
                    Wonder-AI
                  </Title>

                  <div className="contact-item">
                    <Text>
                      <strong>联系方式：</strong> WonderAi(小红书)
                    </Text>
                  </div>

                  <div className="contact-item">
                    <Mail size={16} />
                    <Text>
                      <strong>邮箱：</strong> <EMAIL>
                    </Text>
                  </div>
                </Space>
              </Card>
            </div>

            {/* 版本信息 */}
            <div className={styles.versionInfo}>
              <div className="version-tag">
                <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
                  版本 v1.0.0
                </Tag>
              </div>
              <Text type="secondary">
                最后更新：2025年7月
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;

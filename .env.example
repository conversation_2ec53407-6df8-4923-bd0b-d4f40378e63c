# 数据库配置 (生产环境请使用云数据库)
# 方式一：使用 DATABASE_URL (推荐，适用于云数据库)
DATABASE_URL=mysql://username:password@host:port/database?ssl-mode=REQUIRED

# 方式二：使用单独的环境变量 (可选，作为备用)
DB_HOST=your-database-host
DB_PORT=3306
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=story_ai

# 邮件配置 (用于发送验证码)
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# 加密配置 (请生成一个32位的随机字符串)
ENCRYPTION_KEY=your-32-character-secret-key-here

# 生产环境配置
NODE_ENV=production
DISABLE_ESLINT_PLUGIN=true
GENERATE_SOURCEMAP=false
NODE_NO_WARNINGS=1

import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始为用户表添加角色字段...');

    // 1. 添加role字段
    try {
      const addRoleQuery = `
        ALTER TABLE users
        ADD COLUMN role VARCHAR(20) DEFAULT 'user' COMMENT '用户角色：user-普通用户，admin-管理员'
      `;

      console.log('添加 role 字段...');
      await executeQuery(addRoleQuery);
      console.log('✓ role 字段添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ role 字段已存在，跳过添加');
      } else {
        console.error('添加 role 字段失败:', error);
        throw error;
      }
    }

    // 2. 添加索引
    try {
      const addIndexQuery = `
        ALTER TABLE users
        ADD INDEX idx_role (role)
      `;

      console.log('添加 role 索引...');
      await executeQuery(addIndexQuery);
      console.log('✓ role 索引添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate key name')) {
        console.log('✓ role 索引已存在，跳过添加');
      } else {
        console.error('添加 role 索引失败:', error);
        throw error;
      }
    }

    // 3. 为特定用户设置管理员角色
    try {
      const adminUsernames = ['wkhgogogo', 'weitian'];
      
      for (const username of adminUsernames) {
        const updateAdminQuery = `
          UPDATE users 
          SET role = 'admin' 
          WHERE username = ? AND role != 'admin'
        `;
        
        const result = await executeQuery(updateAdminQuery, [username]);
        console.log(`✓ 用户 ${username} 角色更新结果:`, result);
      }
      
      console.log('✓ 管理员角色设置完成');
    } catch (error: any) {
      console.error('设置管理员角色失败:', error);
      // 这个错误不影响整体流程，继续执行
    }

    return NextResponse.json({
      success: true,
      message: '用户角色字段添加成功',
      details: {
        roleFieldAdded: true,
        indexAdded: true,
        adminRolesSet: true
      }
    });

  } catch (error) {
    console.error('添加用户角色字段失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '添加用户角色字段失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

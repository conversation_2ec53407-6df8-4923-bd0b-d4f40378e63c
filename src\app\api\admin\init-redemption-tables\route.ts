import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始创建兑换码相关表...');

    // 创建兑换码表
    const createRedemptionCodesTable = `
      CREATE TABLE IF NOT EXISTS redemption_codes (
        id VARCHAR(50) PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        code VARCHAR(100) NOT NULL UNIQUE,
        code_type ENUM('duration', 'usage') NOT NULL,
        label VARCHAR(255) NOT NULL,
        -- 时长兑换字段
        duration_days INT NULL,
        -- 次数兑换字段
        usage_count INT NULL,
        -- 安全字段
        hash_code VARCHAR(255) NOT NULL,
        salt VARCHAR(100) NOT NULL,
        -- 状态字段
        is_used BOOLEAN DEFAULT FALSE,
        used_at TIMESTAMP NULL,
        used_by VARCHAR(50) NULL,
        expires_at TIMESTAMP NULL,
        created_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_id (agent_id),
        INDEX idx_code (code),
        INDEX idx_code_type (code_type),
        INDEX idx_created_by (created_by),
        INDEX idx_expires_at (expires_at),
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    console.log('创建 redemption_codes 表...');
    await executeQuery(createRedemptionCodesTable);

    // 创建兑换码使用记录表
    const createRedemptionLogsTable = `
      CREATE TABLE IF NOT EXISTS redemption_logs (
        id VARCHAR(50) PRIMARY KEY,
        redemption_code_id VARCHAR(50) NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        agent_id VARCHAR(50) NOT NULL,
        code_type ENUM('duration', 'usage') NOT NULL,
        -- 兑换获得的内容
        duration_days INT NULL,
        usage_count INT NULL,
        -- 记录字段
        ip_address VARCHAR(45),
        user_agent TEXT,
        redeemed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_redemption_code_id (redemption_code_id),
        INDEX idx_user_id (user_id),
        INDEX idx_agent_id (agent_id),
        INDEX idx_redeemed_at (redeemed_at),
        FOREIGN KEY (redemption_code_id) REFERENCES redemption_codes(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    console.log('创建 redemption_logs 表...');
    await executeQuery(createRedemptionLogsTable);

    // 创建兑换码批次表
    const createRedemptionBatchesTable = `
      CREATE TABLE IF NOT EXISTS redemption_batches (
        id VARCHAR(50) PRIMARY KEY,
        agent_id VARCHAR(50) NOT NULL,
        batch_name VARCHAR(255) NOT NULL,
        code_type ENUM('duration', 'usage') NOT NULL,
        label VARCHAR(255) NOT NULL,
        total_count INT NOT NULL,
        used_count INT DEFAULT 0,
        duration_days INT NULL,
        usage_count INT NULL,
        expires_at TIMESTAMP NULL,
        created_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_agent_id (agent_id),
        INDEX idx_created_by (created_by),
        INDEX idx_code_type (code_type),
        FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    console.log('创建 redemption_batches 表...');
    await executeQuery(createRedemptionBatchesTable);

    // 添加兑换码批次关联字段到兑换码表
    try {
      const addBatchIdColumn = `
        ALTER TABLE redemption_codes 
        ADD COLUMN batch_id VARCHAR(50) NULL,
        ADD INDEX idx_batch_id (batch_id),
        ADD FOREIGN KEY (batch_id) REFERENCES redemption_batches(id) ON DELETE SET NULL
      `;
      
      console.log('添加 batch_id 字段到 redemption_codes 表...');
      await executeQuery(addBatchIdColumn);
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('batch_id 字段已存在，跳过添加');
      } else {
        console.error('添加 batch_id 字段失败:', error);
      }
    }

    console.log('兑换码相关表创建完成！');

    // 检查表结构并添加缺失的字段
    try {
      console.log('检查 redemption_logs 表结构...');
      const tableStructure = await executeQuery('DESCRIBE redemption_logs');
      console.log('redemption_logs 表结构:', tableStructure);

      // 检查是否缺少 duration_days 字段
      const hasDurationDays = tableStructure.some((field: any) => field.Field === 'duration_days');
      if (!hasDurationDays) {
        console.log('添加缺失的 duration_days 字段...');
        await executeQuery('ALTER TABLE redemption_logs ADD COLUMN duration_days INT NULL AFTER code_type');
      }

    } catch (error) {
      console.error('检查表结构失败:', error);
    }

    return NextResponse.json({
      success: true,
      message: '兑换码相关表创建成功',
    });

  } catch (error) {
    console.error('创建兑换码表失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '创建兑换码表失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

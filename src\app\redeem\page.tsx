'use client';

import React from 'react';
import RedeemCodeForm from '@/components/redemption/RedeemCodeForm';
import MainSideBar from '@/components/layout/MainSideBar';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;
  `,
}));



export default function RedeemPage() {
  const { styles } = useStyles();

  return (
    <div className={styles.layout}>
      {/* 最左边的主导航侧边栏 */}
      <MainSideBar />

      <div style={{
        flex: 1,
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '40px 20px'
      }}>
        <RedeemCodeForm />
      </div>
    </div>
  );
}

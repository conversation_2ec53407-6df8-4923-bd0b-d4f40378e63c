#!/bin/bash

# 部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: development, staging, production

set -e

ENVIRONMENT=${1:-production}
PROJECT_NAME="story-ai-studio"

echo "🚀 开始部署 $PROJECT_NAME 到 $ENVIRONMENT 环境..."

# 检查环境
if [ "$ENVIRONMENT" != "development" ] && [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    echo "❌ 错误: 无效的环境参数. 请使用: development, staging, 或 production"
    exit 1
fi

# 检查必要的文件
if [ ! -f ".env.$ENVIRONMENT" ]; then
    echo "❌ 错误: 找不到 .env.$ENVIRONMENT 文件"
    exit 1
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
echo "🧹 清理旧镜像..."
docker image prune -f

# 构建新镜像
echo "🔨 构建新镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查应用健康状态
echo "🏥 检查应用健康状态..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 应用启动成功!"
else
    echo "❌ 应用启动失败，请检查日志:"
    docker-compose logs app
    exit 1
fi

# 显示日志
echo "📋 显示最近的日志:"
docker-compose logs --tail=50 app

echo "🎉 部署完成!"
echo "📱 应用地址: http://localhost:3000"
echo "📊 查看日志: docker-compose logs -f app"
echo "🛑 停止服务: docker-compose down"

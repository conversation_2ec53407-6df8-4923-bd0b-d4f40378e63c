#!/usr/bin/env node

/**
 * 环境变量生成器
 * 用于生成部署所需的环境变量配置
 */

const crypto = require('crypto');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function generateEncryptionKey() {
  return crypto.randomBytes(32).toString('hex');
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🔧 微甜 AI Studio - 环境变量配置生成器\n');
  
  console.log('📋 请提供以下信息来生成环境变量配置：\n');
  
  // 数据库配置
  console.log('🗄️  数据库配置：');
  console.log('选择配置方式：');
  console.log('1. 使用 DATABASE_URL (推荐，适用于云数据库)');
  console.log('2. 使用单独的环境变量');

  const configType = await question('请选择配置方式 (1/2) [1]: ') || '1';

  let databaseUrl = '';
  let dbHost = '';
  let dbPort = '';
  let dbUser = '';
  let dbPassword = '';
  let dbName = '';

  if (configType === '1') {
    databaseUrl = await question('请输入 DATABASE_URL: ');
  } else {
    dbHost = await question('数据库主机地址 (DB_HOST): ');
    dbPort = await question('数据库端口 (DB_PORT) [3306]: ') || '3306';
    dbUser = await question('数据库用户名 (DB_USER): ');
    dbPassword = await question('数据库密码 (DB_PASSWORD): ');
    dbName = await question('数据库名称 (DB_NAME) [story_ai]: ') || 'story_ai';
  }
  
  // API 配置已自动获取，无需手动配置
  
  // 邮件配置
  console.log('\n📧 邮件配置：');
  const smtpHost = await question('SMTP 主机 (SMTP_HOST) [smtp.qq.com]: ') || 'smtp.qq.com';
  const smtpPort = await question('SMTP 端口 (SMTP_PORT) [587]: ') || '587';
  const smtpUser = await question('SMTP 用户名 (SMTP_USER): ');
  const smtpPass = await question('SMTP 密码 (SMTP_PASS): ');
  
  // 生成加密密钥
  const encryptionKey = generateEncryptionKey();
  
  console.log('\n✅ 配置完成！\n');
  
  // 生成环境变量配置
  let databaseConfig = '';
  if (databaseUrl) {
    databaseConfig = `# 数据库配置 (使用 DATABASE_URL)
DATABASE_URL=${databaseUrl}`;
  } else {
    databaseConfig = `# 数据库配置 (使用单独的环境变量)
DB_HOST=${dbHost}
DB_PORT=${dbPort}
DB_USER=${dbUser}
DB_PASSWORD=${dbPassword}
DB_NAME=${dbName}`;
  }

  const envConfig = `# 微甜 AI Studio - 生产环境配置
# 生成时间: ${new Date().toISOString()}

${databaseConfig}

# API 配置 (自动获取当前域名，无需配置)

# 邮件配置
SMTP_HOST=${smtpHost}
SMTP_PORT=${smtpPort}
SMTP_USER=${smtpUser}
SMTP_PASS=${smtpPass}

# 加密配置 (自动生成)
ENCRYPTION_KEY=${encryptionKey}

# 生产环境配置
NODE_ENV=production
DISABLE_ESLINT_PLUGIN=true
GENERATE_SOURCEMAP=false
NODE_NO_WARNINGS=1`;

  console.log('📄 环境变量配置：');
  console.log('=' .repeat(50));
  console.log(envConfig);
  console.log('=' .repeat(50));
  
  console.log('\n📋 Vercel 部署步骤：');
  console.log('1. 复制上面的环境变量配置');
  console.log('2. 在 Vercel 项目设置中逐个添加这些环境变量');
  console.log('3. 或者使用 Vercel CLI 批量设置：');
  console.log('   vercel env add DB_HOST');
  console.log('   vercel env add DB_PORT');
  console.log('   ... (逐个添加)');
  
  console.log('\n🔐 安全提醒：');
  console.log('- 请妥善保管数据库密码和 SMTP 密码');
  console.log('- 加密密钥已自动生成，请勿泄露');
  console.log('- 建议定期更换密码和密钥');
  
  console.log('\n🎉 配置生成完成！');
  
  rl.close();
}

main().catch(console.error);

# 🚀 微甜 AI Studio - Vercel 部署指南

## 📋 部署前准备

### 1. 云数据库准备

由于 Vercel 是无服务器平台，不支持本地 MySQL，你需要使用云数据库：

#### 推荐选项：

**🆓 免费选项：**
- **PlanetScale** - MySQL 兼容，免费 5GB
- **Railway** - 支持 MySQL，免费 $5/月额度
- **Supabase** - PostgreSQL，需要修改代码

**💰 付费选项：**
- **阿里云 RDS** - 稳定可靠
- **腾讯云 MySQL** - 国内访问快
- **AWS RDS** - 全球覆盖

### 2. 获取数据库连接信息

无论选择哪个云数据库，你都需要获取：
- 数据库主机地址 (DB_HOST)
- 端口号 (DB_PORT，通常是 3306)
- 用户名 (DB_USER)
- 密码 (DB_PASSWORD)
- 数据库名 (DB_NAME)

## 🛠️ 部署步骤

### 方法一：使用 Vercel CLI (推荐)

1. **安装 Vercel CLI**
```bash
npm install -g vercel
```

2. **登录 Vercel**
```bash
vercel login
```

3. **运行部署脚本**
```bash
npm run deploy:vercel
```

### 方法二：GitHub 集成部署

1. **推送代码到 GitHub**
```bash
git add .
git commit -m "准备部署到 Vercel"
git push origin main
```

2. **在 Vercel 导入项目**
- 访问 [vercel.com](https://vercel.com)
- 点击 "New Project"
- 选择你的 GitHub 仓库
- 点击 "Import"

## ⚙️ 环境变量配置

在 Vercel 项目设置中添加以下环境变量：

### 必需的环境变量：

#### 数据库配置 (二选一)

**方式一：使用 DATABASE_URL (推荐)**
```env
# 数据库配置 - 使用完整的连接字符串
DATABASE_URL=mysql://username:password@host:port/database?ssl-mode=REQUIRED
```

**方式二：使用单独的环境变量**
```env
# 数据库配置 - 使用单独的变量
DB_HOST=your-cloud-database-host
DB_PORT=3306
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=story_ai
```

#### 其他必需变量
```env
# 邮件配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# 加密密钥 (32位随机字符串)
ENCRYPTION_KEY=your-32-character-secret-key-here
```

### 可选的环境变量：

```env
NODE_ENV=production
DISABLE_ESLINT_PLUGIN=true
GENERATE_SOURCEMAP=false
NODE_NO_WARNINGS=1
```

## 🗄️ 数据库初始化

部署完成后，需要在云数据库中执行初始化：

1. **连接到你的云数据库**
2. **执行初始化脚本**
```sql
-- 复制 scripts/cloud-db-setup.sql 中的内容并执行
```

或者使用数据库管理工具导入 `scripts/cloud-db-setup.sql` 文件。

## 🔧 部署后配置

### 1. 域名配置

✅ **无需手动配置 API 地址** - 系统会自动获取当前域名作为 API 基础 URL。

### 2. 测试功能

部署完成后测试以下功能：
- [ ] 用户注册/登录
- [ ] 聊天功能
- [ ] 模型配置
- [ ] 批量生成
- [ ] 邮件验证

## 🚨 常见问题

### 1. 数据库连接失败
- 检查数据库连接信息是否正确
- 确认数据库允许外部连接
- 检查防火墙设置

### 2. 函数超时
- Vercel 免费版函数限制 10 秒
- 优化代码性能或升级到 Pro 版

### 3. 环境变量不生效
- 确保在 Vercel 控制台正确设置
- 重新部署项目使变量生效

### 4. 邮件发送失败
- 检查 SMTP 配置
- 确认邮箱服务商设置

## 📊 监控和维护

### 查看部署日志
```bash
vercel logs your-project-url
```

### 本地测试
```bash
vercel dev
```

### 重新部署
```bash
npm run deploy:prod
```

## 🎯 优化建议

1. **性能优化**
   - 启用 Vercel Analytics
   - 使用 CDN 加速静态资源
   - 优化图片和字体

2. **安全优化**
   - 定期更新依赖包
   - 使用强密码和密钥
   - 启用 HTTPS

3. **监控优化**
   - 设置错误监控
   - 配置性能监控
   - 定期备份数据库

---

🎉 **部署完成！** 你的微甜 AI Studio 现在已经在 Vercel 上运行了！

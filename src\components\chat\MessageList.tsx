'use client';

import React from 'react';
import { createStyles } from 'antd-style';
import { Message } from '@/types';
import MessageItem from './MessageItem';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    flex: 1;
    overflow-y: auto;
    padding: ${token.padding}px;
    display: flex;
    flex-direction: column;
    gap: ${token.marginMD}px;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: ${token.colorBorder};
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: ${token.colorBorderSecondary};
    }
  `,
  
  empty: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: ${token.colorTextTertiary};
    font-size: ${token.fontSizeLG}px;
    
    .icon {
      font-size: 48px;
      margin-bottom: ${token.marginLG}px;
      opacity: 0.5;
    }
  `,
  
  loadingMore: css`
    text-align: center;
    padding: ${token.paddingMD}px;
    color: ${token.colorTextTertiary};
  `,
}));

interface MessageListProps {
  messages: Message[];
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  loading = false,
  onLoadMore,
  hasMore = false,
}) => {
  const { styles } = useStyles();
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (messages.length === 0 && !loading) {
    return (
      <div className={styles.empty}>
        <div className="icon">💬</div>
        <div>开始你的第一次对话吧</div>
        <div style={{ fontSize: '14px', marginTop: '8px', opacity: 0.7 }}>
          输入消息或上传文件来开始聊天
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {hasMore && (
        <div className={styles.loadingMore}>
          {loading ? '加载中...' : (
            <button 
              onClick={onLoadMore}
              style={{ 
                background: 'none', 
                border: 'none', 
                color: 'inherit',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
            >
              加载更多消息
            </button>
          )}
        </div>
      )}
      
      {messages.map((message) => (
        <MessageItem
          key={message.id}
          message={message}
        />
      ))}
      
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
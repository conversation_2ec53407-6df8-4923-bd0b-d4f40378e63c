import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始删除智能体tags字段...');
    
    // 删除tags列
    const dropColumnQuery = `ALTER TABLE agents DROP COLUMN tags`;
    
    try {
      console.log('执行删除tags列:', dropColumnQuery);
      const result = await executeQuery<any>(dropColumnQuery);
      console.log('删除结果:', result);
      
      return NextResponse.json({
        success: true,
        message: 'Tags字段已成功删除',
      });
      
    } catch (error) {
      console.error('删除tags列失败:', error);
      
      // 如果列不存在，也算成功
      if (error instanceof Error && error.message.includes("doesn't exist")) {
        return NextResponse.json({
          success: true,
          message: 'Tags字段不存在，无需删除',
        });
      }
      
      throw error;
    }

  } catch (error) {
    console.error('删除tags字段失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '删除失败',
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { Agent } from '@/types/agent';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// JWT验证中间件
const verifyToken = (request: NextRequest) => {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    return null;
  }
};

// GET - 获取所有智能体
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const enabled = searchParams.get('enabled');

    let query = 'SELECT * FROM agents WHERE 1=1';
    const params: any[] = [];

    // 分类筛选
    if (category && category !== 'all') {
      query += ' AND category = ?';
      params.push(category);
    }

    // 搜索筛选
    if (search) {
      query += ' AND (name LIKE ? OR description LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm);
    }

    // 启用状态筛选
    if (enabled !== null && enabled !== undefined) {
      query += ' AND enabled = ?';
      params.push(enabled === 'true');
    }

    query += ' ORDER BY created_at DESC';

    const agents = await executeQuery<any[]>(query, params);

    // 转换数据格式
    const formattedAgents: Agent[] = agents.map(agent => {
      // 安全解析 preset_prompts
      let presetPrompts: string[] = [];
      if (agent.preset_prompts) {
        // 检查是否已经是对象（MySQL JSON字段会自动解析）
        if (Array.isArray(agent.preset_prompts)) {
          presetPrompts = agent.preset_prompts;
        } else if (typeof agent.preset_prompts === 'string') {
          // 如果是字符串，尝试解析JSON
          try {
            const parsed = JSON.parse(agent.preset_prompts);
            presetPrompts = Array.isArray(parsed) ? parsed : [];
          } catch (error) {
            presetPrompts = [];
          }
        } else {
          presetPrompts = [];
        }
      }

      return {
        id: agent.id,
        name: agent.name,
        description: agent.description,
        category: agent.category,
        type: agent.agent_type || 'coze',
        avatar: agent.avatar,
        usageInstructions: agent.usage_instructions,
        presetPrompts,
        purchaseLink: agent.purchase_link,
        cozeConfig: agent.agent_type === 'coze' ? {
          apiKey: agent.coze_api_key || '',
          botId: agent.coze_bot_id || '',
          userId: agent.coze_user_id || '',
        } : undefined,
        builtinConfig: agent.agent_type === 'builtin' ? {
          secretCode: agent.secret_code || '',
          systemPrompt: agent.system_prompt || '',
        } : undefined,
        createdAt: agent.created_at.toISOString(),
        updatedAt: agent.updated_at.toISOString(),
        enabled: Boolean(agent.enabled),
        usageCount: agent.usage_count || 0,
        trialUsageCount: agent.trial_usage_count || 3,
      };
    });

    return NextResponse.json({
      success: true,
      data: formattedAgents,
    });
  } catch (error) {
    console.error('获取智能体失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取智能体失败',
      },
      { status: 500 }
    );
  }
}

// POST - 创建新智能体
export async function POST(request: NextRequest) {
  try {
    // 验证JWT token
    const user = verifyToken(request);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      category,
      type = 'coze',
      avatar,
      usageInstructions,
      presetPrompts,
      purchaseLink,
      cozeApiKey,
      cozeBotId,
      cozeUserId,
      secretCode,
      systemPrompt,
      enabled = true,
      trialUsageCount = 3
    } = body;



    if (!name || !category) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体名称和分类为必填项',
        },
        { status: 400 }
      );
    }

    const id = generateId();
    const now = new Date();

    const query = `
      INSERT INTO agents (
        id, name, description, category, agent_type, avatar, usage_instructions, preset_prompts, purchase_link, coze_api_key, coze_bot_id, coze_user_id,
        secret_code, system_prompt, enabled, usage_count, trial_usage_count, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      name,
      description || null,
      category,
      type,
      avatar || null,
      usageInstructions || null,
      presetPrompts ? JSON.stringify(presetPrompts) : null,
      purchaseLink || null,
      cozeApiKey || null,
      cozeBotId || null,
      cozeUserId || null,
      secretCode || null,
      systemPrompt || null,
      Boolean(enabled),
      0,
      trialUsageCount,
      now,
      now,
    ];

    await executeQuery(query, params);

    const newAgent: Agent = {
      id,
      name,
      description,
      category,
      avatar,
      type,
      usageInstructions,
      presetPrompts,
      purchaseLink,
      cozeConfig: type === 'coze' ? {
        apiKey: cozeApiKey || '',
        botId: cozeBotId || '',
        userId: cozeUserId || '',
      } : undefined,
      builtinConfig: type === 'builtin' ? {
        secretCode: secretCode || '',
        systemPrompt: systemPrompt || '',
      } : undefined,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
      enabled: Boolean(enabled),
      usageCount: 0,
      trialUsageCount,
    };

    return NextResponse.json({
      success: true,
      data: newAgent,
    });
  } catch (error) {
    console.error('创建智能体失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '创建智能体失败',
      },
      { status: 500 }
    );
  }
}

// PUT - 更新智能体
export async function PUT(request: NextRequest) {
  try {
    // 验证JWT token
    const user = verifyToken(request);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      id,
      name,
      description,
      category,
      type,
      avatar,
      usageInstructions,
      presetPrompts,
      purchaseLink,
      cozeApiKey,
      cozeBotId,
      cozeUserId,
      secretCode,
      systemPrompt,
      enabled,
      usageCount,
      trialUsageCount
    } = body;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 动态构建更新查询，只更新提供的字段
    const updateFields: string[] = [];
    const params: any[] = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      params.push(name);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      params.push(description || null);
    }

    if (category !== undefined) {
      updateFields.push('category = ?');
      params.push(category);
    }

    if (type !== undefined) {
      updateFields.push('agent_type = ?');
      params.push(type);
    }

    if (avatar !== undefined) {
      updateFields.push('avatar = ?');
      params.push(avatar || null);
    }

    if (usageInstructions !== undefined) {
      updateFields.push('usage_instructions = ?');
      params.push(usageInstructions || null);
    }

    if (presetPrompts !== undefined) {
      updateFields.push('preset_prompts = ?');
      params.push(presetPrompts ? JSON.stringify(presetPrompts) : null);
    }

    if (purchaseLink !== undefined) {
      updateFields.push('purchase_link = ?');
      params.push(purchaseLink || null);
    }

    if (cozeApiKey !== undefined) {
      updateFields.push('coze_api_key = ?');
      params.push(cozeApiKey || null);
    }

    if (cozeBotId !== undefined) {
      updateFields.push('coze_bot_id = ?');
      params.push(cozeBotId || null);
    }

    if (cozeUserId !== undefined) {
      updateFields.push('coze_user_id = ?');
      params.push(cozeUserId || null);
    }

    if (secretCode !== undefined) {
      updateFields.push('secret_code = ?');
      params.push(secretCode || null);
    }

    if (systemPrompt !== undefined) {
      updateFields.push('system_prompt = ?');
      params.push(systemPrompt || null);
    }

    if (enabled !== undefined) {
      updateFields.push('enabled = ?');
      params.push(Boolean(enabled));
    }

    if (usageCount !== undefined) {
      updateFields.push('usage_count = ?');
      params.push(usageCount || 0);
    }

    if (trialUsageCount !== undefined) {
      updateFields.push('trial_usage_count = ?');
      params.push(trialUsageCount || 3);
    }

    // 总是更新 updated_at
    updateFields.push('updated_at = ?');
    params.push(new Date());

    // 添加 WHERE 条件的参数
    params.push(id);

    if (updateFields.length === 1) { // 只有 updated_at，说明没有实际要更新的字段
      return NextResponse.json(
        {
          success: false,
          error: '没有提供要更新的字段',
        },
        { status: 400 }
      );
    }

    const query = `
      UPDATE agents
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    const result = await executeQuery<any>(query, params);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体不存在',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '智能体更新成功',
    });
  } catch (error) {
    console.error('更新智能体失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '更新智能体失败',
      },
      { status: 500 }
    );
  }
}

// DELETE - 删除智能体
export async function DELETE(request: NextRequest) {
  try {
    // 验证JWT token
    const user = verifyToken(request);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 先删除相关的定价方案
    await executeQuery('DELETE FROM agent_pricing_plans WHERE agent_id = ?', [id]);

    // 删除智能体
    const query = 'DELETE FROM agents WHERE id = ?';
    const result = await executeQuery<any>(query, [id]);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体不存在',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '智能体删除成功',
    });
  } catch (error) {
    console.error('删除智能体失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '删除智能体失败',
      },
      { status: 500 }
    );
  }
}

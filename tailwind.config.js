/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Lobe Chat 主色调 - 蓝色系
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#1a73e8', // Lobe Chat 主色
          600: '#1557b0', // Lobe Chat 主色 hover
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        // 功能色彩
        success: {
          DEFAULT: '#34a853',
          light: '#4caf50',
          dark: '#2e7d32',
        },
        warning: {
          DEFAULT: '#fbbc04',
          light: '#ffc107',
          dark: '#f57c00',
        },
        error: {
          DEFAULT: '#ea4335',
          light: '#f44336',
          dark: '#d32f2f',
        },
        info: {
          DEFAULT: '#1a73e8',
          light: '#2196f3',
          dark: '#1976d2',
        },
        // 中性色 - Google Material Design
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        // 文字颜色
        text: {
          primary: '#202124',
          secondary: '#5f6368',
          tertiary: '#80868b',
          quaternary: '#9aa0a6',
        },
        // 背景色
        bg: {
          base: '#ffffff',
          container: '#ffffff',
          elevated: '#ffffff',
          layout: '#f8f9fa',
          spotlight: '#ffffff',
        },
        // 边框色
        border: {
          DEFAULT: '#dadce0',
          secondary: '#e8eaed',
        },
        // 填充色
        fill: {
          DEFAULT: '#f1f3f4',
          secondary: '#f8f9fa',
          tertiary: '#ffffff',
          quaternary: '#ffffff',
        },
      },
      fontFamily: {
        sans: ['Google Sans', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      borderRadius: {
        'xs': '4px',
        'sm': '6px',
        'DEFAULT': '8px',
        'lg': '12px',
      },
      boxShadow: {
        'soft': '0 2px 8px rgba(0, 0, 0, 0.06)',
        'medium': '0 4px 16px rgba(0, 0, 0, 0.08)',
        'large': '0 8px 32px rgba(0, 0, 0, 0.12)',
        'hover': '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'spin': 'spin 1s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      boxShadow: {
        'soft': '0 2px 8px rgba(0, 0, 0, 0.06)',
        'medium': '0 4px 16px rgba(0, 0, 0, 0.08)',
        'large': '0 8px 32px rgba(0, 0, 0, 0.12)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
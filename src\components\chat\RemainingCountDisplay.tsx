'use client';

import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Tag, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { agentRemainingApi, agentsApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { useChatStore } from '@/store/chat';
import { Agent } from '@/types/agent';

const useStyles = createStyles(({ token }) => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '4px 8px',
    borderRadius: '6px',
    backgroundColor: token.colorBgContainer,
    border: `1px solid ${token.colorBorder}`,
    fontSize: '12px',
  },
  
  subscriptionTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  trialTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  exhaustedTag: {
    margin: 0,
    fontSize: '12px',
  },
  
  upgradeButton: {
    height: '24px',
    fontSize: '12px',
    padding: '0 8px',
  },
}));

interface RemainingCountDisplayProps {
  agentId: string;
  className?: string;
}

export interface RemainingCountDisplayRef {
  refresh: () => void;
}

interface RemainingData {
  agentId: string;
  agentName: string;
  hasSubscription: boolean;
  subscriptionType?: 'time_based' | 'usage_based';
  remainingCount: number | null;
  expiryDate?: string;
  trialCount: number;
  usedCount?: number;
  status: 'subscribed' | 'trial_available' | 'trial_exhausted';
  isRedemptionUser?: boolean;
  redemptionInfo?: {
    id: string;
    code_type: 'duration' | 'usage';
    duration_days?: number;
    usage_count?: number;
    redeemed_at: string;
    label: string;
  };
}

const RemainingCountDisplay = forwardRef<RemainingCountDisplayRef, RemainingCountDisplayProps>(
  ({ agentId, className }, ref) => {
    const { styles } = useStyles();
    const [remainingData, setRemainingData] = useState<RemainingData | null>(null);
    const [loading, setLoading] = useState(false);
    const [agentInfo, setAgentInfo] = useState<Agent | null>(null);
    const { user } = useAuthStore();
    const { isLoading, currentSessions, activeSessionId } = useChatStore();

    useEffect(() => {
      if (user && agentId) {
        fetchRemainingCount();
      }
    }, [user, agentId]);

    // 监听消息发送状态，当发送完成后刷新剩余次数
    useEffect(() => {
      // 当 isLoading 从 true 变为 false 时，说明消息发送完成
      if (!isLoading && user && agentId) {
        // 延迟一点时间再刷新，确保后端数据已更新
        const timer = setTimeout(() => {
          fetchRemainingCount();
        }, 500);

        return () => clearTimeout(timer);
      }
    }, [isLoading, user, agentId]);

    const fetchRemainingCount = async () => {
      if (!user) return;

      setLoading(true);
      try {
        // 获取智能体信息
        const agentsResult = await agentsApi.getAll();
        if (agentsResult.success && agentsResult.data) {
          const agent = agentsResult.data.find(a => a.id === agentId);
          if (agent) {
            setAgentInfo(agent);
          }
        }

        // 获取剩余次数信息
        const result = await agentRemainingApi.getRemainingCount(user.id, agentId);
        if (result.success && result.data) {
          setRemainingData(result.data);
        }
      } catch (error) {
        console.error('获取剩余次数失败:', error);
      } finally {
        setLoading(false);
      }
    };

    // 暴露刷新方法给父组件
    useImperativeHandle(ref, () => ({
      refresh: fetchRemainingCount
    }));

  const handleUpgrade = () => {
    if (agentInfo?.purchaseLink) {
      window.open(agentInfo.purchaseLink, '_blank');
    } else {
      console.log('跳转到订阅页面 - 暂无购买链接');
    }
  };

  if (loading || !remainingData) {
    return null;
  }

  const { hasSubscription, remainingCount, status, trialCount, usedCount, subscriptionType, expiryDate, isRedemptionUser, redemptionInfo } = remainingData;

  // 已订阅用户
  if (hasSubscription) {
    if (subscriptionType === 'time_based') {
      const expiryDateObj = expiryDate ? new Date(expiryDate) : null;
      const daysLeft = expiryDateObj ? Math.ceil((expiryDateObj.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0;

      // 当剩余时长大于0时，优先展示剩余时长；只有过期时才展示剩余次数
      if (daysLeft > 0) {
        return (
          <div className={`${styles.container} ${className}`}>
            <Tag color="green" className={styles.subscriptionTag}>
              已订阅
            </Tag>
            <Tooltip title={`订阅到期时间：${expiryDateObj?.toLocaleDateString()}`}>
              <span>剩余 {daysLeft} 天</span>
            </Tooltip>
          </div>
        );
      } else {
        return (
          <div className={`${styles.container} ${className}`}>
            <Tag color="orange" className={styles.subscriptionTag}>
              已订阅
            </Tag>
            <span>剩余 {remainingCount || 0} 次</span>
            <Tooltip title={`订阅已过期：${expiryDateObj?.toLocaleDateString()}`}>
              <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
            </Tooltip>
          </div>
        );
      }
    } else {
      return (
        <div className={`${styles.container} ${className}`}>
          <Tag color="green" className={styles.subscriptionTag}>
            已订阅
          </Tag>
          <span>剩余 {remainingCount || '无限'} 次</span>
        </div>
      );
    }
  }

  // 兑换码用户（显示为会员）
  if (isRedemptionUser && status === 'trial_available') {
    const color = remainingCount && remainingCount <= 1 ? 'red' : remainingCount && remainingCount <= 3 ? 'orange' : 'green';

    // 如果是时长兑换码，计算剩余天数
    if (redemptionInfo && redemptionInfo.code_type === 'duration' && redemptionInfo.duration_days && redemptionInfo.redeemed_at) {
      const redeemedDate = new Date(redemptionInfo.redeemed_at);
      const expiryDate = new Date(redeemedDate.getTime() + redemptionInfo.duration_days * 24 * 60 * 60 * 1000);
      const daysLeft = Math.ceil((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));

      // 当剩余时长大于0时，优先展示剩余时长；只有过期时才展示剩余次数
      if (daysLeft > 0) {
        // 显示剩余时长
        return (
          <div className={`${styles.container} ${className}`}>
            <Tag color="green" className={styles.subscriptionTag}>
              会员
            </Tag>
            <span>剩余 {daysLeft} 天</span>
            <Tooltip title={`兑换码权益：${redemptionInfo.label}`}>
              <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
            </Tooltip>
          </div>
        );
      } else {
        return (
          <div className={`${styles.container} ${className}`}>
            <Tag color="orange" className={styles.subscriptionTag}>
              会员
            </Tag>
            <span>剩余 {remainingCount} 次</span>
            <Tooltip title={`兑换码权益已过期：${redemptionInfo.label}`}>
              <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
            </Tooltip>
          </div>
        );
      }
    }

    // 次数兑换码或无时长信息时，显示剩余次数
    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color="green" className={styles.subscriptionTag}>
          会员
        </Tag>
        <span>剩余 {remainingCount} 次</span>
        {redemptionInfo && (
          <Tooltip title={`兑换码权益：${redemptionInfo.label}`}>
            <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
          </Tooltip>
        )}
      </div>
    );
  }

  // 试用用户
  if (status === 'trial_available') {
    const color = remainingCount && remainingCount <= 1 ? 'red' : remainingCount && remainingCount <= 3 ? 'orange' : 'blue';

    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color={color} className={styles.trialTag}>
          试用
        </Tag>
        <span>剩余 {remainingCount} / {trialCount} 次</span>
        <Tooltip title="试用次数用完后需要购买订阅">
          <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
        </Tooltip>
        {remainingCount && remainingCount <= 3 && (
          <Button
            type="primary"
            size="small"
            icon={<ShoppingCartOutlined />}
            className={styles.upgradeButton}
            onClick={handleUpgrade}
          >
            升级
          </Button>
        )}
      </div>
    );
  }

  // 兑换码用户次数用完（显示为会员）
  if (isRedemptionUser && status === 'trial_exhausted') {
    let tooltipTitle = `兑换码权益已用完：${redemptionInfo?.label || ''}`;

    // 如果是时长兑换码，检查是否过期
    if (redemptionInfo && redemptionInfo.code_type === 'duration' && redemptionInfo.duration_days && redemptionInfo.redeemed_at) {
      const redeemedDate = new Date(redemptionInfo.redeemed_at);
      const expiryDate = new Date(redeemedDate.getTime() + redemptionInfo.duration_days * 24 * 60 * 60 * 1000);
      const daysLeft = Math.ceil((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));

      if (daysLeft <= 0) {
        tooltipTitle = `兑换码权益已过期：${redemptionInfo.label}`;
      }
    }

    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color="orange" className={styles.subscriptionTag}>
          会员
        </Tag>
        <span>剩余 0 次</span>
        {redemptionInfo && (
          <Tooltip title={tooltipTitle}>
            <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
          </Tooltip>
        )}
        <Button
          type="primary"
          size="small"
          icon={<ShoppingCartOutlined />}
          className={styles.upgradeButton}
          onClick={handleUpgrade}
        >
          续费
        </Button>
      </div>
    );
  }

  // 试用已用完
  if (status === 'trial_exhausted') {
    return (
      <div className={`${styles.container} ${className}`}>
        <Tag color="red" className={styles.exhaustedTag}>
          试用已用完
        </Tag>
        <span>{usedCount} / {trialCount} 次</span>
        <Button
          type="primary"
          size="small"
          icon={<ShoppingCartOutlined />}
          className={styles.upgradeButton}
          onClick={handleUpgrade}
        >
          立即购买
        </Button>
      </div>
    );
  }

    return null;
  }
);

RemainingCountDisplay.displayName = 'RemainingCountDisplay';

export default RemainingCountDisplay;

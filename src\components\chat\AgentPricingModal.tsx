'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Card, Tag, Descriptions, Spin, Alert, Button } from 'antd';
import { ShoppingCartOutlined, ClockCircleOutlined, NumberOutlined } from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { agentRemainingApi, agentsApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { Agent } from '@/types/agent';

const useStyles = createStyles(({ token }) => ({
  pricingCard: {
    marginBottom: '16px',
    border: `1px solid ${token.colorBorder}`,
    borderRadius: '8px',
  },
  
  pricingHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '12px',
  },
  
  pricingTitle: {
    fontSize: '16px',
    fontWeight: 600,
    color: token.colorText,
    margin: 0,
  },
  
  priceTag: {
    fontSize: '14px',
    fontWeight: 600,
  },
  
  usageInfo: {
    padding: '16px',
    backgroundColor: token.colorFillTertiary,
    borderRadius: '8px',
    marginBottom: '16px',
  },
  
  usageTitle: {
    fontSize: '14px',
    fontWeight: 600,
    color: token.colorText,
    marginBottom: '8px',
  },
  
  statusTag: {
    marginBottom: '8px',
  },
}));

interface AgentPricingModalProps {
  visible: boolean;
  onClose: () => void;
  agentId: string;
  agentName: string;
}

interface PricingPlan {
  id: string;
  name: string;
  type: 'per_usage' | 'time_based';
  usageCount?: number;
  pricePerUsage?: number | string;
  durationDays?: number;
  pricePerPeriod?: number | string;
  currency: string;
  description?: string;
}

interface UsageInfo {
  hasSubscription: boolean;
  subscriptionType?: 'time_based' | 'usage_based';
  remainingCount: number | null;
  expiryDate?: string;
  trialCount: number;
  usedCount?: number;
  status: 'subscribed' | 'trial_available' | 'trial_exhausted';
}

export default function AgentPricingModal({ visible, onClose, agentId, agentName }: AgentPricingModalProps) {
  const { styles } = useStyles();
  const [loading, setLoading] = useState(false);
  const [usageInfo, setUsageInfo] = useState<UsageInfo | null>(null);
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [agentInfo, setAgentInfo] = useState<Agent | null>(null);
  const { user } = useAuthStore();

  useEffect(() => {
    if (visible && user && agentId) {
      fetchData();
    }
  }, [visible, user, agentId]);

  const fetchData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // 获取智能体信息
      const agentsResult = await agentsApi.getAll();
      if (agentsResult.success && agentsResult.data) {
        const agent = agentsResult.data.find(a => a.id === agentId);
        if (agent) {
          setAgentInfo(agent);
        }
      }

      // 获取用户使用情况
      const usageResult = await agentRemainingApi.getRemainingCount(user.id, agentId);
      if (usageResult.success && usageResult.data) {
        setUsageInfo(usageResult.data);
      }

      // 获取定价方案
      const pricingResponse = await fetch(`/api/agent-pricing?agentId=${agentId}`);
      const pricingResult = await pricingResponse.json();
      if (pricingResult.success && pricingResult.data) {
        setPricingPlans(pricingResult.data);
      }
    } catch (error) {
      console.error('获取定价信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number | string, currency: string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return `¥${numPrice.toFixed(2)}`;
  };

  const formatExpiryDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} 天后到期`;
    } else {
      return '已到期';
    }
  };

  const getStatusTag = (status: string, hasSubscription: boolean) => {
    if (hasSubscription) {
      return <Tag color="green" className={styles.statusTag}>已订阅</Tag>;
    }
    
    switch (status) {
      case 'trial_available':
        return <Tag color="blue" className={styles.statusTag}>试用中</Tag>;
      case 'trial_exhausted':
        return <Tag color="red" className={styles.statusTag}>试用已用完</Tag>;
      default:
        return <Tag color="default" className={styles.statusTag}>未知状态</Tag>;
    }
  };

  return (
    <Modal
      title={`${agentName} - 定价信息`}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={600}
    >
      <Spin spinning={loading}>
        {/* 当前使用情况 */}
        {usageInfo && (
          <div className={styles.usageInfo}>
            <div className={styles.usageTitle}>当前使用情况</div>
            {getStatusTag(usageInfo.status, usageInfo.hasSubscription)}
            
            <Descriptions column={1} size="small">
              {usageInfo.hasSubscription ? (
                <>
                  <Descriptions.Item label="订阅类型">
                    {usageInfo.subscriptionType === 'time_based' ? '按时间订阅' : '按次数订阅'}
                  </Descriptions.Item>
                  {usageInfo.subscriptionType === 'time_based' && usageInfo.expiryDate && (
                    <Descriptions.Item label="剩余时间">
                      {formatExpiryDate(usageInfo.expiryDate)}
                    </Descriptions.Item>
                  )}
                  {usageInfo.subscriptionType === 'usage_based' && (
                    <Descriptions.Item label="剩余次数">
                      {usageInfo.remainingCount || '无限制'}
                    </Descriptions.Item>
                  )}
                </>
              ) : (
                <>
                  <Descriptions.Item label="试用次数">
                    {usageInfo.trialCount} 次
                  </Descriptions.Item>
                  <Descriptions.Item label="已使用">
                    {usageInfo.usedCount || 0} 次
                  </Descriptions.Item>
                  <Descriptions.Item label="剩余次数">
                    {usageInfo.remainingCount || 0} 次
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>
          </div>
        )}

        {/* 定价方案 */}
        <div>
          <h4 style={{ marginBottom: '16px' }}>可用定价方案</h4>
          {pricingPlans.length > 0 ? (
            pricingPlans.map((plan) => (
              <Card key={plan.id} className={styles.pricingCard} size="small">
                <div className={styles.pricingHeader}>
                  <h5 className={styles.pricingTitle}>{plan.name}</h5>
                  <Tag 
                    color={plan.type === 'per_usage' ? 'blue' : 'green'} 
                    className={styles.priceTag}
                  >
                    {plan.type === 'per_usage' 
                      ? formatPrice(plan.pricePerUsage || 0, plan.currency)
                      : formatPrice(plan.pricePerPeriod || 0, plan.currency)
                    }
                  </Tag>
                </div>
                
                <Descriptions column={2} size="small">
                  <Descriptions.Item 
                    label={<span><NumberOutlined /> 计费方式</span>}
                    span={2}
                  >
                    {plan.type === 'per_usage' ? '按次计费' : '按时间计费'}
                  </Descriptions.Item>
                  
                  {plan.type === 'per_usage' ? (
                    <>
                      <Descriptions.Item label="使用次数">
                        {plan.usageCount} 次
                      </Descriptions.Item>
                      <Descriptions.Item label="单次价格">
                        {formatPrice(plan.pricePerUsage || 0, plan.currency)}
                      </Descriptions.Item>
                    </>
                  ) : (
                    <>
                      <Descriptions.Item label="有效期">
                        {plan.durationDays} 天
                      </Descriptions.Item>
                      <Descriptions.Item label="期间价格">
                        {formatPrice(plan.pricePerPeriod || 0, plan.currency)}
                      </Descriptions.Item>
                    </>
                  )}
                  
                  {plan.description && (
                    <Descriptions.Item label="说明" span={2}>
                      {plan.description}
                    </Descriptions.Item>
                  )}
                </Descriptions>
                
                <Button
                  type="primary"
                  icon={<ShoppingCartOutlined />}
                  style={{ marginTop: '12px' }}
                  onClick={() => {
                    if (agentInfo?.purchaseLink) {
                      window.open(agentInfo.purchaseLink, '_blank');
                    } else {
                      console.log('购买方案:', plan.id, '- 暂无购买链接');
                    }
                  }}
                >
                  购买此方案
                </Button>
              </Card>
            ))
          ) : (
            <Alert
              message="暂无定价方案"
              description="该智能体暂未设置定价方案，请联系管理员。"
              type="info"
              showIcon
            />
          )}
        </div>
      </Spin>
    </Modal>
  );
}

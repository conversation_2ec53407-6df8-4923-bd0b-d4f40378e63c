import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 检查智能体状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 查询智能体状态
    const query = `
      SELECT 
        id,
        name,
        enabled,
        created_at,
        updated_at
      FROM agents 
      WHERE id = ?
      LIMIT 1
    `;

    const agents = await executeQuery<any[]>(query, [agentId]);

    if (agents.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          exists: false,
          enabled: false,
          agentId,
          name: null,
          reason: 'agent_not_found'
        }
      });
    }

    const agent = agents[0];
    const isEnabled = Boolean(agent.enabled);

    return NextResponse.json({
      success: true,
      data: {
        exists: true,
        enabled: isEnabled,
        agentId: agent.id,
        name: agent.name,
        reason: !isEnabled ? 'agent_disabled' : null
      }
    });

  } catch (error) {
    console.error('检查智能体状态失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '检查智能体状态失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

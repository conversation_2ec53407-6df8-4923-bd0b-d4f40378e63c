import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';

// GET - 获取智能体的定价方案
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    const query = `
      SELECT * FROM agent_pricing_plans
      WHERE agent_id = ? AND is_active = true
      ORDER BY created_at DESC
    `;

    const rawPlans = await executeQuery<any[]>(query, [agentId]);

    // 转换字段名为驼峰命名
    const plans = rawPlans.map(plan => ({
      id: plan.id,
      agentId: plan.agent_id,
      name: plan.name,
      type: plan.type,
      usageCount: plan.usage_count,
      pricePerUsage: plan.price_per_usage,
      durationDays: plan.duration_days,
      pricePerPeriod: plan.price_per_period,
      currency: plan.currency,
      description: plan.description,
      isActive: plan.is_active,
      createdAt: plan.created_at,
      updatedAt: plan.updated_at,
    }));

    return NextResponse.json({
      success: true,
      data: plans,
    });

  } catch (error) {
    console.error('获取定价方案失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取定价方案失败',
      },
      { status: 500 }
    );
  }
}

// POST - 创建新的定价方案
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      agentId,
      name,
      type,
      usageCount,
      pricePerUsage,
      durationDays,
      pricePerPeriod,
      currency = 'CNY',
      description,
      isActive = true
    } = body;

    if (!agentId || !name || !type) {
      return NextResponse.json(
        {
          success: false,
          error: '智能体ID、方案名称和类型为必填项',
        },
        { status: 400 }
      );
    }

    // 验证定价方案类型和必填字段
    if (type === 'per_usage' && (!usageCount || !pricePerUsage)) {
      return NextResponse.json(
        {
          success: false,
          error: '按次收费方案需要提供使用次数和单次价格',
        },
        { status: 400 }
      );
    }

    if (type === 'time_based' && (!durationDays || !pricePerPeriod)) {
      return NextResponse.json(
        {
          success: false,
          error: '按时计费方案需要提供时长和期间价格',
        },
        { status: 400 }
      );
    }

    const id = generateId();
    const now = new Date();

    const query = `
      INSERT INTO agent_pricing_plans (
        id, agent_id, name, type, usage_count, price_per_usage, 
        duration_days, price_per_period, currency, description, 
        is_active, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      agentId,
      name,
      type,
      type === 'per_usage' ? usageCount : null,
      type === 'per_usage' ? pricePerUsage : null,
      type === 'time_based' ? durationDays : null,
      type === 'time_based' ? pricePerPeriod : null,
      currency,
      description || null,
      Boolean(isActive),
      now,
      now,
    ];

    await executeQuery(query, params);

    return NextResponse.json({
      success: true,
      data: { id },
      message: '定价方案创建成功',
    });

  } catch (error) {
    console.error('创建定价方案失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '创建定价方案失败',
      },
      { status: 500 }
    );
  }
}

// PUT - 更新定价方案
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      id,
      name,
      type,
      usageCount,
      pricePerUsage,
      durationDays,
      pricePerPeriod,
      currency,
      description,
      isActive
    } = body;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '定价方案ID为必填项',
        },
        { status: 400 }
      );
    }

    const query = `
      UPDATE agent_pricing_plans 
      SET name = ?, type = ?, usage_count = ?, price_per_usage = ?, 
          duration_days = ?, price_per_period = ?, currency = ?, 
          description = ?, is_active = ?, updated_at = ?
      WHERE id = ?
    `;

    const params = [
      name,
      type,
      type === 'per_usage' ? usageCount : null,
      type === 'per_usage' ? pricePerUsage : null,
      type === 'time_based' ? durationDays : null,
      type === 'time_based' ? pricePerPeriod : null,
      currency || 'CNY',
      description || null,
      Boolean(isActive),
      new Date(),
      id,
    ];

    await executeQuery(query, params);

    return NextResponse.json({
      success: true,
      message: '定价方案更新成功',
    });

  } catch (error) {
    console.error('更新定价方案失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '更新定价方案失败',
      },
      { status: 500 }
    );
  }
}

// DELETE - 删除定价方案
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '定价方案ID为必填项',
        },
        { status: 400 }
      );
    }

    const query = `DELETE FROM agent_pricing_plans WHERE id = ?`;
    await executeQuery(query, [id]);

    return NextResponse.json({
      success: true,
      message: '定价方案删除成功',
    });

  } catch (error) {
    console.error('删除定价方案失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '删除定价方案失败',
      },
      { status: 500 }
    );
  }
}

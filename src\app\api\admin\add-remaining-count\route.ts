import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始为 agent_usage_logs 表添加剩余次数字段...');

    // 1. 添加剩余次数字段
    try {
      const addRemainingCountQuery = `
        ALTER TABLE agent_usage_logs
        ADD COLUMN remaining_count INT DEFAULT NULL COMMENT '剩余使用次数，NULL表示无限制（已付费）'
      `;

      console.log('添加 remaining_count 字段...');
      await executeQuery(addRemainingCountQuery);
      console.log('✓ remaining_count 字段添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✓ remaining_count 字段已存在，跳过添加');
      } else {
        console.error('添加 remaining_count 字段失败:', error);
        throw error;
      }
    }

    // 2. 添加索引
    try {
      const addIndexQuery = `
        ALTER TABLE agent_usage_logs
        ADD INDEX idx_remaining_count (remaining_count)
      `;

      console.log('添加索引...');
      await executeQuery(addIndexQuery);
      console.log('✓ 索引添加成功');
    } catch (error: any) {
      if (error.message.includes('Duplicate key name')) {
        console.log('✓ 索引已存在，跳过添加');
      } else {
        console.error('添加索引失败:', error);
        throw error;
      }
    }

    // 3. 创建计算剩余次数的函数
    try {
      const createFunctionQuery = `
        CREATE OR REPLACE FUNCTION CalculateRemainingCount(
            p_user_id VARCHAR(50),
            p_agent_id VARCHAR(50)
        ) RETURNS INT
        READS SQL DATA
        DETERMINISTIC
        BEGIN
            DECLARE v_trial_count INT DEFAULT 3;
            DECLARE v_used_count INT DEFAULT 0;
            DECLARE v_has_subscription BOOLEAN DEFAULT FALSE;
            DECLARE v_remaining INT DEFAULT 0;
            
            -- 获取智能体的试用次数
            SELECT trial_usage_count INTO v_trial_count
            FROM agents 
            WHERE id = p_agent_id
            LIMIT 1;
            
            -- 如果没有找到智能体，设置默认试用次数
            IF v_trial_count IS NULL THEN
                SET v_trial_count = 3;
            END IF;
            
            -- 检查用户是否有有效的付费订阅
            SELECT COUNT(*) > 0 INTO v_has_subscription
            FROM user_agent_subscriptions 
            WHERE user_id = p_user_id 
              AND agent_id = p_agent_id 
              AND status = 'active'
              AND (
                  -- 按次计费且有剩余次数
                  (remaining_usage > 0) OR
                  -- 按时计费且未过期
                  (end_date IS NOT NULL AND end_date > NOW())
              )
            LIMIT 1;
            
            -- 如果有付费订阅，返回 NULL 表示无限制
            IF v_has_subscription THEN
                RETURN NULL;
            END IF;
            
            -- 获取已使用次数
            SELECT COALESCE(SUM(usage_count), 0) INTO v_used_count
            FROM agent_usage_logs 
            WHERE user_id = p_user_id 
              AND agent_id = p_agent_id;
            
            -- 计算剩余次数
            SET v_remaining = v_trial_count - v_used_count;
            
            -- 确保剩余次数不为负数
            IF v_remaining < 0 THEN
                SET v_remaining = 0;
            END IF;
            
            RETURN v_remaining;
        END
      `;

      console.log('创建计算剩余次数函数...');
      await executeQuery(createFunctionQuery);
      console.log('✓ 函数创建成功');
    } catch (error: any) {
      console.error('创建函数失败:', error);
      // 函数创建失败不影响整体流程，继续执行
    }

    // 4. 创建触发器（插入时）
    try {
      const createTriggerQuery = `
        CREATE OR REPLACE TRIGGER tr_agent_usage_logs_update_remaining
        BEFORE INSERT ON agent_usage_logs
        FOR EACH ROW
        BEGIN
            SET NEW.remaining_count = CalculateRemainingCount(NEW.user_id, NEW.agent_id);
        END
      `;

      console.log('创建插入触发器...');
      await executeQuery(createTriggerQuery);
      console.log('✓ 插入触发器创建成功');
    } catch (error: any) {
      console.error('创建插入触发器失败:', error);
      // 触发器创建失败不影响整体流程
    }

    // 5. 创建触发器（更新时）
    try {
      const createUpdateTriggerQuery = `
        CREATE OR REPLACE TRIGGER tr_agent_usage_logs_update_remaining_on_update
        BEFORE UPDATE ON agent_usage_logs
        FOR EACH ROW
        BEGIN
            SET NEW.remaining_count = CalculateRemainingCount(NEW.user_id, NEW.agent_id);
        END
      `;

      console.log('创建更新触发器...');
      await executeQuery(createUpdateTriggerQuery);
      console.log('✓ 更新触发器创建成功');
    } catch (error: any) {
      console.error('创建更新触发器失败:', error);
      // 触发器创建失败不影响整体流程
    }

    console.log('✅ 剩余次数字段添加完成');

    return NextResponse.json({
      success: true,
      message: '剩余次数字段添加成功',
    });
  } catch (error) {
    console.error('添加剩余次数字段失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '添加剩余次数字段失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

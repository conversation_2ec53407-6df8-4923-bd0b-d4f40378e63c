import { Prompt } from '@/types';

export interface PromptsData {
  prompts: Prompt[];
  version: string;
}

class FileManager<T> {
  private storageKey: string;

  constructor(storageKey: string) {
    this.storageKey = storageKey;
  }

  // 从localStorage读取数据
  load(): T | null {
    try {
      if (typeof window === 'undefined') return null;
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Error loading data from ${this.storageKey}:`, error);
      return null;
    }
  }

  // 保存数据到localStorage
  save(data: T): void {
    try {
      if (typeof window === 'undefined') return;
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error(`Error saving data to ${this.storageKey}:`, error);
    }
  }

  // 删除数据
  remove(): void {
    try {
      if (typeof window === 'undefined') return;
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.error(`Error removing data from ${this.storageKey}:`, error);
    }
  }
}

// 创建提示词文件管理器实例
export const promptsFileManager = new FileManager<PromptsData>('prompts-data');

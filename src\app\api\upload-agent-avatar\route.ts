import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const agentId = formData.get('agentId') as string;

    if (!file) {
      return NextResponse.json({ error: '没有找到文件' }, { status: 400 });
    }

    if (!agentId) {
      return NextResponse.json({ error: '缺少智能体ID' }, { status: 400 });
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: '不支持的文件类型，请上传图片文件' }, { status: 400 });
    }

    // 验证文件大小 (最大 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: '文件大小不能超过5MB' }, { status: 400 });
    }

    // 获取文件扩展名
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'png';
    
    // 构建文件路径
    const uploadsDir = join(process.cwd(), 'public', 'agents_shortcut');
    const fileName = `${agentId}.${fileExtension}`;
    const filePath = join(uploadsDir, fileName);

    // 确保目录存在
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }

    // 将文件转换为 Buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // 写入文件
    await writeFile(filePath, buffer);

    // 返回相对路径
    const relativePath = `/agents_shortcut/${fileName}`;

    return NextResponse.json({ 
      success: true, 
      url: relativePath,
      message: '图标上传成功' 
    });

  } catch (error) {
    console.error('上传图标失败:', error);
    return NextResponse.json({ 
      error: '上传失败，请重试' 
    }, { status: 500 });
  }
}

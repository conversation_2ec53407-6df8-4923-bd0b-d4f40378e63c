version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: story-ai-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: story_ai
      MYSQL_USER: story_user
      MYSQL_PASSWORD: story_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./mysql-config/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - story-ai-network
    command: --default-authentication-plugin=mysql_native_password --max_connections=500

  # Redis 缓存服务（可选，用于会话存储）
  redis:
    image: redis:7-alpine
    container_name: story-ai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - story-ai-network

  # Next.js 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: story-ai-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=story_user
      - DB_PASSWORD=story_password
      - DB_NAME=story_ai
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - NEXTAUTH_SECRET=your-super-secret-nextauth-key-change-in-production
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - mysql
      - redis
    networks:
      - story-ai-network
    volumes:
      - ./uploads:/app/uploads

volumes:
  mysql_data:
  redis_data:

networks:
  story-ai-network:
    driver: bridge

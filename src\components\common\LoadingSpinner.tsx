'use client';

import React from 'react';
import { Spin } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    
    .ant-spin {
      .ant-spin-dot {
        font-size: 24px;
      }
    }
  `,
  
  fullScreen: css`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .ant-spin {
      .ant-spin-dot {
        font-size: 32px;
      }
    }
  `,
  
  inline: css`
    display: inline-flex;
    align-items: center;
    gap: ${token.marginSM}px;
  `,
}));

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  fullScreen?: boolean;
  inline?: boolean;
  spinning?: boolean;
  children?: React.ReactNode;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip = '加载中...',
  fullScreen = false,
  inline = false,
  spinning = true,
  children,
}) => {
  const { styles, cx } = useStyles();

  if (!spinning && !children) {
    return null;
  }

  const spinnerClass = cx(
    !children && (fullScreen ? styles.fullScreen : inline ? styles.inline : styles.container)
  );

  if (children) {
    return (
      <Spin spinning={spinning} tip={tip} size={size}>
        {children}
      </Spin>
    );
  }

  return (
    <div className={spinnerClass}>
      <Spin size={size} />
      {tip && <div style={{ marginTop: 8, textAlign: 'center' }}>{tip}</div>}
    </div>
  );
};

export default LoadingSpinner;

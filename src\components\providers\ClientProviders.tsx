'use client';

import React, { useEffect } from 'react';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider, App } from 'antd';
import ServiceInitializer from './ServiceInitializer';
import LobeThemeProvider from './LobeThemeProvider';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
  // 在客户端组件中也抑制警告
  useEffect(() => {
    // 保存原始方法的引用
    const originalWarn = console.warn;
    const originalError = console.error;

    // 创建过滤函数
    const filteredWarn = (...args: any[]) => {
      const message = args[0];
      if (typeof message === 'string' && (
        message.includes('antd v5 support React is 16') ||
        message.includes('compatible') ||
        message.includes('https://u.ant.design/v5-for-19') ||
        message.includes('antd: compatible') ||
        message.includes('Encountered two children with the same key') ||
        message.includes('Keys should be unique') ||
        message.includes('Fast Refresh') ||
        message.includes('cleanup function')
      )) {
        return;
      }
      originalWarn.apply(console, args);
    };

    const filteredError = (...args: any[]) => {
      const message = args[0];
      if (typeof message === 'string' && (
        message.includes('antd v5 support React is 16') ||
        message.includes('compatible') ||
        message.includes('https://u.ant.design/v5-for-19') ||
        message.includes('antd: compatible') ||
        message.includes('Encountered two children with the same key') ||
        message.includes('Keys should be unique') ||
        message.includes('cleanup function')
      )) {
        return;
      }
      originalError.apply(console, args);
    };

    // 替换 console 方法
    console.warn = filteredWarn;
    console.error = filteredError;

    // 返回清理函数
    return () => {
      // 确保在组件卸载时恢复原始方法
      if (console.warn === filteredWarn) {
        console.warn = originalWarn;
      }
      if (console.error === filteredError) {
        console.error = originalError;
      }
    };
  }, []);

  return (
    <AntdRegistry>
      <LobeThemeProvider
        defaultAppearance="light"
        defaultPrimaryColor=""
        defaultNeutralColor=""
      >
        <ConfigProvider>
          <App>
            <ServiceInitializer />
            {children}
          </App>
        </ConfigProvider>
      </LobeThemeProvider>
    </AntdRegistry>
  );
}
'use client';

import { ActionIcon } from '@lobehub/ui';
import { useTheme } from 'antd-style';
import { Moon, Sun, Monitor } from 'lucide-react';
import { memo, useState } from 'react';
import { Dropdown, type MenuProps } from 'antd';

const ThemeSwitch = memo(() => {
  const theme = useTheme();
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'auto'>('light');

  const handleThemeChange = (mode: 'light' | 'dark' | 'auto') => {
    setThemeMode(mode);
    
    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('LOBE_THEME_APPEARANCE', mode);
      
      // 如果是 auto 模式，根据系统主题设置
      if (mode === 'auto') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', systemTheme);
      } else {
        document.documentElement.setAttribute('data-theme', mode);
      }
    }
  };

  const themeIcons = {
    light: Sun,
    dark: Moon,
    auto: Monitor,
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'light',
      label: '浅色主题',
      icon: <Sun size={16} />,
      onClick: () => handleThemeChange('light'),
    },
    {
      key: 'dark',
      label: '深色主题',
      icon: <Moon size={16} />,
      onClick: () => handleThemeChange('dark'),
    },
    {
      key: 'auto',
      label: '跟随系统',
      icon: <Monitor size={16} />,
      onClick: () => handleThemeChange('auto'),
    },
  ];

  const CurrentIcon = themeIcons[themeMode];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <ActionIcon
        icon={CurrentIcon}
        size={24}
        style={{
          color: theme.colorText,
          backgroundColor: theme.colorFillTertiary,
          border: `1px solid ${theme.colorBorder}`,
        }}
        title="切换主题"
      />
    </Dropdown>
  );
});

ThemeSwitch.displayName = 'ThemeSwitch';

export default ThemeSwitch;

'use client';

import { DraggablePanel, DraggablePanelContainer, type DraggablePanelProps } from '@lobehub/ui';
import { createStyles, useResponsive } from 'antd-style';
import { PropsWithChildren, memo, useState } from 'react';
const FOLDER_WIDTH = 270;

export const useStyles = createStyles(({ css, token }) => ({
  panel: css`
    height: 100%;
    color: ${token.colorTextSecondary};
    background: ${token.colorBgLayout};
  `,
}));

const SessionPanel = memo<PropsWithChildren>(({ children }) => {
  const { md = true } = useResponsive();
  const { styles } = useStyles();
  const [sessionsWidth, setSessionsWidth] = useState(FOLDER_WIDTH);
  const [sessionExpandable, setSessionExpandable] = useState(true);

  const handleExpand = (expand: boolean) => {
    setSessionExpandable(expand);
  };

  const handleSizeChange: DraggablePanelProps['onSizeChange'] = (_, size) => {
    if (!size) return;
    const nextWidth = typeof size.width === 'string' ? Number.parseInt(size.width) : size.width;
    if (!nextWidth) return;
    setSessionsWidth(nextWidth);
  };

  return (
    <DraggablePanel
      className={styles.panel}
      defaultSize={{ width: sessionsWidth }}
      expand={sessionExpandable}
      expandable={true}
      maxWidth={400}
      minWidth={FOLDER_WIDTH}
      mode={md ? 'fixed' : 'float'}
      onExpandChange={handleExpand}
      onSizeChange={handleSizeChange}
      placement="left"
      size={{ height: '100%', width: sessionsWidth }}
    >
      <DraggablePanelContainer style={{ flex: 'none', height: '100%', minWidth: FOLDER_WIDTH }}>
        {children}
      </DraggablePanelContainer>
    </DraggablePanel>
  );
});

export default SessionPanel;

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Agent, AgentStore, CreateAgentForm, AgentCategory } from '@/types/agent';
import { useChatStore } from './chat';
import { agentsApi } from '@/lib/api';

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useAgentStore = create<AgentStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      agents: [],
      selectedCategory: 'all',
      searchQuery: '',
      loading: false,
      error: null,

      // 获取智能体列表（只获取已启用的智能体）
      fetchAgents: async () => {
        set({ loading: true, error: null });
        try {
          const result = await agentsApi.getAll({ enabled: true });
          if (result.success && result.data) {
            set({ agents: result.data, loading: false });
          } else {
            set({ error: result.error || '获取智能体列表失败', loading: false });
          }
        } catch (error) {
          set({ error: '获取智能体列表失败', loading: false });
        }
      },

      // 添加智能体
      addAgent: async (agentForm: CreateAgentForm) => {
        set({ loading: true, error: null });
        try {
          const result = await agentsApi.create(agentForm);
          if (result.success && result.data) {
            set((state) => ({
              agents: [...state.agents, result.data!],
              loading: false,
            }));
            return result.data;
          } else {
            set({ error: result.error || '添加智能体失败', loading: false });
            throw new Error(result.error || '添加智能体失败');
          }
        } catch (error) {
          set({ error: '添加智能体失败', loading: false });
          throw error;
        }
      },

      // 更新智能体
      updateAgent: async (id: string, updates: Partial<Agent>) => {
        set({ loading: true, error: null });
        try {
          const result = await agentsApi.update(id, updates);
          if (result.success) {
            set((state) => ({
              agents: state.agents.map(agent =>
                agent.id === id
                  ? { ...agent, ...updates, updatedAt: new Date().toISOString() }
                  : agent
              ),
              loading: false,
            }));
          } else {
            set({ error: result.error || '更新智能体失败', loading: false });
          }
        } catch (error) {
          set({ error: '更新智能体失败', loading: false });
        }
      },

      // 删除智能体
      deleteAgent: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const result = await agentsApi.delete(id);
          if (result.success) {
            set((state) => ({
              agents: state.agents.filter(agent => agent.id !== id),
              loading: false,
            }));
          } else {
            set({ error: result.error || '删除智能体失败', loading: false });
          }
        } catch (error) {
          set({ error: '删除智能体失败', loading: false });
        }
      },

      // 设置选中的分类
      setSelectedCategory: (category: AgentCategory | 'all') => {
        set({ selectedCategory: category });
      },

      // 设置搜索查询
      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },

      // 添加智能体到会话
      addAgentToSession: async (agentId: string) => {
        try {
          const agent = get().agents.find(a => a.id === agentId);
          if (!agent) {
            throw new Error('智能体不存在');
          }

          // 获取聊天store
          const chatStore = useChatStore.getState();

          // 创建或切换到智能体会话（如果已存在则切换，不存在则创建）
          const sessionId = chatStore.createSession({
            title: `与 ${agent.name} 对话`,
            agentId: agent.id,
            agentName: agent.name,
            agentDescription: agent.description,
            agentCategory: agent.category,
          });

          // 增加使用次数
          get().incrementUsageCount(agentId);

          return sessionId;
        } catch (error) {
          set({ error: '添加智能体到会话失败' });
          throw error;
        }
      },

      // 增加使用次数
      incrementUsageCount: async (agentId: string) => {
        try {
          await agentsApi.incrementUsage(agentId);
          // 更新本地状态
          set((state) => ({
            agents: state.agents.map(agent =>
              agent.id === agentId
                ? { ...agent, usageCount: (agent.usageCount || 0) + 1 }
                : agent
            ),
          }));
        } catch (error) {
          console.error('增加使用次数失败:', error);
        }
      },
    }),
    {
      name: 'agent-store',
      partialize: (state) => ({
        agents: state.agents,
        selectedCategory: state.selectedCategory,
      }),
    }
  )
);

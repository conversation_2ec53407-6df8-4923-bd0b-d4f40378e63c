import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 生成验证token
const generateVerificationToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// 邮箱验证正则
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 密码强度验证
const validatePassword = (password: string) => {
  if (password.length < 8) {
    return '密码长度至少8位';
  }
  if (!/(?=.*[a-z])/.test(password)) {
    return '密码必须包含小写字母';
  }
  if (!/(?=.*[A-Z])/.test(password)) {
    return '密码必须包含大写字母';
  }
  if (!/(?=.*\d)/.test(password)) {
    return '密码必须包含数字';
  }
  return null;
};

// POST - 用户注册
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, email, password, confirmPassword, verificationCode } = body;

    // 基础验证
    if (!username || !email || !password || !confirmPassword || !verificationCode) {
      return NextResponse.json(
        {
          success: false,
          error: '所有字段都是必填的',
        },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱格式不正确',
        },
        { status: 400 }
      );
    }

    // 密码确认验证
    if (password !== confirmPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '两次输入的密码不一致',
        },
        { status: 400 }
      );
    }

    // 密码强度验证
    const passwordError = validatePassword(password);
    if (passwordError) {
      return NextResponse.json(
        {
          success: false,
          error: passwordError,
        },
        { status: 400 }
      );
    }

    // 验证邮箱验证码
    const verificationResult = await executeQuery(
      'SELECT * FROM email_verification_codes WHERE email = ? AND code = ? AND expires_at > NOW() ORDER BY created_at DESC LIMIT 1',
      [email, verificationCode]
    );

    if (verificationResult.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '验证码无效或已过期',
        },
        { status: 400 }
      );
    }

    // 检查用户名是否已存在
    const existingUsername = await executeQuery(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUsername.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名已存在',
        },
        { status: 409 }
      );
    }

    // 检查邮箱是否已存在
    const existingEmail = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingEmail.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱已被注册',
        },
        { status: 409 }
      );
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 生成验证token
    const verificationToken = generateVerificationToken();
    const verificationExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

    // 创建用户
    const userId = generateId();
    const now = new Date();

    const query = `
      INSERT INTO users (
        id, username, email, password_hash, is_verified, 
        verification_token, verification_expires_at, provider, 
        created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      userId,
      username,
      email,
      passwordHash,
      true, // 邮箱已通过验证码验证
      verificationToken,
      verificationExpiresAt,
      'local',
      now,
      now,
    ];

    await executeQuery(query, params);

    // 删除已使用的验证码
    await executeQuery(
      'DELETE FROM email_verification_codes WHERE email = ?',
      [email]
    );

    return NextResponse.json({
      success: true,
      message: '注册成功！邮箱已验证，可以直接登录',
      data: {
        userId,
        username,
        email,
        needsVerification: false,
      },
    });
  } catch (error) {
    console.error('用户注册失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '注册失败，请重试',
      },
      { status: 500 }
    );
  }
}

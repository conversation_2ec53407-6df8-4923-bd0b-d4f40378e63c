'use client';

import React, { useEffect, useState } from 'react';
import { Result, Button } from 'antd';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/hooks/usePermissions';
import { Permission } from '@/hooks/usePermissions';
import { useAuthStore } from '@/store/auth';

interface PermissionGuardProps {
  permission: Permission;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 权限守卫组件
 * 检查用户是否有指定权限，如果没有则显示无权限页面
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  children,
  fallback
}) => {
  const { hasPermission } = usePermissions();
  const { user, isLoggedIn, checkLoginStatus } = useAuthStore();
  const router = useRouter();
  const [isInitializing, setIsInitializing] = useState(true);

  // 快速初始化认证状态
  useEffect(() => {
    const initAuth = () => {
      // 如果已经有用户信息，直接跳过初始化
      if (user && isLoggedIn) {
        setIsInitializing(false);
        return;
      }

      // 快速检查本地存储是否有有效的认证信息
      const authToken = localStorage.getItem('auth_token');
      const authUser = localStorage.getItem('auth_user');
      const loginTime = localStorage.getItem('auth_login_time');

      if (authToken && authUser && loginTime) {
        // 有认证信息，异步恢复状态但不阻塞UI
        checkLoginStatus().catch(console.error);
      }

      // 立即结束初始化状态，让组件渲染
      setIsInitializing(false);
    };

    initAuth();
  }, [user, isLoggedIn, checkLoginStatus]);

  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '60vh',
          padding: '20px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '48px',
              height: '48px',
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #1890ff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }} />
            <p style={{ color: '#666', margin: 0 }}>正在验证权限...</p>
          </div>
        </div>
      </>
    );
  }

  // 如果用户还没有登录，重定向到登录页面
  if (!isLoggedIn || !user) {
    router.replace('/auth');
    return null;
  }

  if (!hasPermission(permission)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        padding: '20px'
      }}>
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => router.push('/chat')}>
              返回首页
            </Button>
          }
        />
      </div>
    );
  }

  return <>{children}</>;
};

export default PermissionGuard;

import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import nodemailer from 'nodemailer';

// 邮箱验证正则
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 生成6位数验证码
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 创建邮件发送器
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.qq.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// 发送验证码邮件
const sendVerificationEmail = async (email: string, code: string) => {
  const transporter = createTransporter();
  
  const mailOptions = {
    from: `"微甜 AI Studio" <${process.env.SMTP_USER}>`,
    to: email,
    subject: '邮箱验证码 - 微甜 AI Studio',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #1890ff; margin: 0;">微甜 AI Studio</h1>
          <p style="color: #666; margin: 10px 0;">让创意无限可能</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center;">
          <h2 style="color: #333; margin-bottom: 20px;">邮箱验证码</h2>
          <p style="color: #666; margin-bottom: 30px;">您正在注册微甜 AI Studio 账户，请使用以下验证码完成注册：</p>
          
          <div style="background: #fff; padding: 20px; border-radius: 6px; margin: 20px 0; border: 2px dashed #1890ff;">
            <span style="font-size: 32px; font-weight: bold; color: #1890ff; letter-spacing: 8px;">${code}</span>
          </div>
          
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            验证码有效期为 10 分钟，请及时使用。<br>
            如果您没有申请此验证码，请忽略此邮件。
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
          <p>此邮件由系统自动发送，请勿回复。</p>
          <p>© 2025 微甜 AI Studio. All rights reserved.</p>
        </div>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};

// POST - 发送验证码
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // 基础验证
    if (!email) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱地址是必填的',
        },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱格式不正确',
        },
        { status: 400 }
      );
    }

    // 检查邮箱是否已被注册
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: '该邮箱已被注册',
        },
        { status: 409 }
      );
    }

    // 生成验证码
    const verificationCode = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

    // 删除该邮箱之前的验证码记录
    await executeQuery(
      'DELETE FROM email_verification_codes WHERE email = ?',
      [email]
    );

    // 保存验证码到数据库
    await executeQuery(
      'INSERT INTO email_verification_codes (email, code, expires_at, created_at) VALUES (?, ?, ?, ?)',
      [email, verificationCode, expiresAt, new Date()]
    );

    // 发送验证码邮件
    try {
      // 在开发环境中，同时输出到控制台和发送邮件（如果配置了SMTP）
      if (process.env.NODE_ENV === 'development') {
        console.log(`\n=== 邮箱验证码 ===`);
        console.log(`邮箱: ${email}`);
        console.log(`验证码: ${verificationCode}`);
        console.log(`有效期: 10分钟`);
        console.log(`==================\n`);

        // 如果配置了SMTP，也尝试发送邮件
        if (process.env.SMTP_USER && process.env.SMTP_PASS &&
            process.env.SMTP_USER !== '<EMAIL>') {
          try {
            await sendVerificationEmail(email, verificationCode);
            console.log('邮件发送成功！');
          } catch (emailError) {
            console.log('邮件发送失败，但验证码已在控制台输出');
          }
        }
      } else {
        await sendVerificationEmail(email, verificationCode);
      }
    } catch (emailError) {
      console.error('发送邮件失败:', emailError);
      return NextResponse.json(
        {
          success: false,
          error: '发送验证码失败，请检查邮箱地址或稍后重试',
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '验证码已发送到您的邮箱，请查收',
    });
  } catch (error) {
    console.error('发送验证码失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '发送验证码失败，请重试',
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { executeQuery } from '@/lib/db';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// POST - 刷新token
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少认证token',
        },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    
    try {
      // 验证当前token（即使过期也要能解析出用户信息）
      const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true }) as any;
      
      // 检查用户是否仍然存在且已验证
      const users = await executeQuery(
        'SELECT id, username, email, is_verified FROM users WHERE id = ? AND is_verified = 1',
        [decoded.userId]
      );

      if (users.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: '用户不存在或未验证',
          },
          { status: 401 }
        );
      }

      const user = users[0];

      // 检查token是否在合理的过期时间内（允许在过期后1小时内刷新）
      const now = Math.floor(Date.now() / 1000);
      const tokenExp = decoded.exp;
      const oneHourInSeconds = 60 * 60;
      
      if (now - tokenExp > oneHourInSeconds) {
        return NextResponse.json(
          {
            success: false,
            error: 'Token过期时间过长，请重新登录',
          },
          { status: 401 }
        );
      }

      // 生成新的token
      const newTokenPayload = {
        userId: user.id,
        username: user.username,
        email: user.email,
      };

      // 保持原有的过期时间策略
      const rememberMe = request.headers.get('x-remember-me') === 'true';
      const expiresIn = rememberMe ? '7d' : '24h';
      const newToken = jwt.sign(newTokenPayload, JWT_SECRET, { expiresIn });

      return NextResponse.json({
        success: true,
        data: {
          token: newToken,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
          },
        },
      });

    } catch (jwtError) {
      // JWT验证失败
      return NextResponse.json(
        {
          success: false,
          error: 'Token无效',
        },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Token刷新失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '服务器错误',
      },
      { status: 500 }
    );
  }
}

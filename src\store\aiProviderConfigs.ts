import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AIProviderConfig, AIProvider, UserSettings } from '@/types/ai-provider';
import { aiProvidersApi, userSettingsApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';

interface AIProviderConfigsState {
  // 状态
  configs: AIProviderConfig[];
  userSettings: UserSettings | null;
  loading: boolean;
  error: string | null;
  
  // 操作
  loadConfigs: (userId: string) => Promise<void>;
  saveConfig: (config: Omit<AIProviderConfig, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  deleteConfig: (userId: string, provider: AIProvider) => Promise<void>;
  loadUserSettings: (userId: string) => Promise<void>;
  saveUserSettings: (settings: Partial<UserSettings> & { userId: string }) => Promise<void>;
  updateCurrentModel: (provider: AIProvider, model: string) => Promise<void>;
  getProviderConfig: (provider: AIProvider) => AIProviderConfig | null;
  isProviderEnabled: (provider: AIProvider) => boolean;
  clearError: () => void;
}

export const useAIProviderConfigsStore = create<AIProviderConfigsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      configs: [],
      userSettings: null,
      loading: false,
      error: null,

      // 加载用户的AI提供商配置
      loadConfigs: async (userId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.getConfigs(userId);
          if (response.success && response.data) {
            set({ configs: response.data, loading: false });
          } else {
            set({ error: response.error || '加载配置失败', loading: false });
          }
        } catch (error) {
          console.error('加载AI提供商配置失败:', error);
          set({ error: '加载配置失败', loading: false });
        }
      },

      // 保存AI提供商配置
      saveConfig: async (config) => {
        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.saveConfig(config);
          if (response.success) {
            // 重新加载配置
            await get().loadConfigs(config.userId);
          } else {
            set({ error: response.error || '保存配置失败', loading: false });
          }
        } catch (error) {
          console.error('保存AI提供商配置失败:', error);
          set({ error: '保存配置失败', loading: false });
        }
      },

      // 删除AI提供商配置
      deleteConfig: async (userId: string, provider: AIProvider) => {
        set({ loading: true, error: null });
        try {
          const response = await aiProvidersApi.deleteConfig(userId, provider);
          if (response.success) {
            // 重新加载配置
            await get().loadConfigs(userId);
          } else {
            set({ error: response.error || '删除配置失败', loading: false });
          }
        } catch (error) {
          console.error('删除AI提供商配置失败:', error);
          set({ error: '删除配置失败', loading: false });
        }
      },

      // 加载用户设置
      loadUserSettings: async (userId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await userSettingsApi.getSettings(userId);
          if (response.success && response.data) {
            set({ userSettings: response.data, loading: false });
          } else {
            set({ error: response.error || '加载用户设置失败', loading: false });
          }
        } catch (error) {
          console.error('加载用户设置失败:', error);
          set({ error: '加载用户设置失败', loading: false });
        }
      },

      // 保存用户设置
      saveUserSettings: async (settings) => {
        set({ loading: true, error: null });
        try {
          const response = await userSettingsApi.saveSettings(settings);
          if (response.success) {
            // 重新加载用户设置
            await get().loadUserSettings(settings.userId);
          } else {
            set({ error: response.error || '保存用户设置失败', loading: false });
          }
        } catch (error) {
          console.error('保存用户设置失败:', error);
          set({ error: '保存用户设置失败', loading: false });
        }
      },

      // 更新当前模型
      updateCurrentModel: async (provider: AIProvider, model: string) => {
        const { userSettings } = get();
        const authUser = useAuthStore.getState().user;
        const userId = userSettings?.userId || authUser?.id;

        if (userId) {
          await get().saveUserSettings({
            userId,
            currentProvider: provider,
            currentModel: model,
          });
        } else {
          console.error('无法更新当前模型：缺少用户ID');
        }
      },

      // 获取指定提供商的配置
      getProviderConfig: (provider: AIProvider) => {
        const { configs } = get();
        return configs.find(config => config.provider === provider) || null;
      },

      // 检查提供商是否启用
      isProviderEnabled: (provider: AIProvider) => {
        const config = get().getProviderConfig(provider);
        return config ? config.enabled : false;
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'ai-provider-configs-storage',
      // 只持久化用户设置，配置信息从数据库加载
      partialize: (state) => ({
        userSettings: state.userSettings,
      }),
    }
  )
);

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// 消息相关类型
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  files?: FileInfo[];
  status?: 'sending' | 'sent' | 'error';
  isAgentDescription?: boolean; // 标识是否为智能体描述消息
}

export interface ChatSession {
  id: string;
  sessionUniqueId: string; // 新增的会话唯一ID，确保每个会话实例都有唯一标识
  title: string;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
  // AI模型相关信息
  provider?: AIProvider;
  model?: string;
  // 智能体相关信息
  agentId?: string;
  agentName?: string;
  agentDescription?: string;
  agentCategory?: string;
}

// 文件相关类型
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  uri?: string;
  mimeType?: string;
  status: 'selected' | 'uploading' | 'uploaded' | 'error';
}

// 批量生成相关类型
export interface BatchItem {
  id: string;
  input?: string;
  prompt: string;
  variables?: Record<string, string>;
  result?: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  createdAt: number;
  startTime?: number;
  endTime?: number;
  error?: string;
}

export interface BatchTemplate {
  id: string;
  name: string;
  description: string;
  prompt: string;
  variables: string[];
  category: string;
  createdAt: number;
}

export interface BatchTask {
  id: string;
  prompt?: string;
  items: BatchItem[];
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error';
  currentIndex: number;
  createdAt: string;
  updatedAt: string;
}

// 提示词相关类型
export interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags?: string[];
  isFavorite: boolean;
  isPublic?: boolean;
  createdAt: string;
  updatedAt: string;
}

// AI 提供商类型
export type AIProvider = 'openai' | 'anthropic' | 'google' | 'deepseek';

// 注意：AI模型相关的类型定义已移至 src/types/ai-provider.ts

// API 配置相关类型（保持向后兼容）
export interface ApiConfig {
  apiKey: string;
  apiUrl: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

export interface GeminiModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  supportFiles: boolean;
}

// 应用设置相关类型
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  fontSize: 'small' | 'medium' | 'large';
  autoSave: boolean;
  notifications: boolean;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

// 状态管理相关类型
export interface AuthState {
  isLoggedIn: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface ChatState {
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  messages: Message[];
  isGenerating: boolean;
  selectedFiles: FileInfo[];
  error: string | null;
}

export interface BatchState {
  items: BatchItem[];
  templates: BatchTemplate[];
  isGenerating: boolean;
  isPaused: boolean;
  currentBatchIndex: number;
  progress: { current: number; total: number };
  loading: boolean;
  error: string | null;
}

export interface SettingsState {
  apiConfig: ApiConfig;
  appSettings: AppSettings;
  models: GeminiModel[];
  loading: boolean;
  error: string | null;
}

export interface PromptsState {
  prompts: Prompt[];
  selectedPrompt: Prompt | null;
  loading: boolean;
  error: string | null;
}

// 工具函数类型
export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
}

// 组件 Props 类型
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  width?: number | string;
  footer?: React.ReactNode;
}

// 路由相关类型
export type RouteParams = {
  [key: string]: string | string[] | undefined;
};

// 导出格式类型
export type ExportFormat = 'txt' | 'docx' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  includePrompts?: boolean;
  includeTimestamps?: boolean;
  filename?: string;
}
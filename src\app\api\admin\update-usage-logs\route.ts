import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('开始更新 agent_usage_logs 表结构...');

    // 1. 检查并添加次数字段
    try {
      const addUsageCountQuery = `
        ALTER TABLE agent_usage_logs
        ADD COLUMN usage_count INT DEFAULT 1 COMMENT '本次使用次数'
      `;

      console.log('添加 usage_count 字段...');
      await executeQuery(addUsageCountQuery);
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('usage_count 字段已存在，跳过添加');
      } else {
        throw error;
      }
    }

    // 2. 检查并添加截止时间字段
    try {
      const addExpiryDateQuery = `
        ALTER TABLE agent_usage_logs
        ADD COLUMN expiry_date TIMESTAMP NULL COMMENT '截止时间，用于定时定价模式'
      `;

      console.log('添加 expiry_date 字段...');
      await executeQuery(addExpiryDateQuery);
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('expiry_date 字段已存在，跳过添加');
      } else {
        throw error;
      }
    }

    // 3. 检查并添加索引
    try {
      const addIndexQuery = `
        ALTER TABLE agent_usage_logs
        ADD INDEX idx_expiry_date (expiry_date)
      `;

      console.log('添加索引...');
      await executeQuery(addIndexQuery);
    } catch (error: any) {
      if (error.message.includes('Duplicate key name')) {
        console.log('idx_expiry_date 索引已存在，跳过添加');
      } else {
        throw error;
      }
    }

    // 4. 更新现有记录的 usage_count
    const updateExistingQuery = `
      UPDATE agent_usage_logs 
      SET usage_count = 1 
      WHERE usage_count IS NULL OR usage_count = 0
    `;
    
    const updateResult = await executeQuery(updateExistingQuery);

    // 5. 创建用户智能体使用统计视图
    const createViewQuery = `
      CREATE OR REPLACE VIEW user_agent_usage_summary AS
      SELECT 
          user_id,
          agent_id,
          COUNT(*) as total_sessions,
          SUM(usage_count) as total_usage_count,
          SUM(tokens_used) as total_tokens,
          SUM(cost) as total_cost,
          MIN(created_at) as first_usage,
          MAX(created_at) as last_usage,
          MAX(CASE 
              WHEN expiry_date IS NOT NULL AND expiry_date > NOW() 
              THEN expiry_date 
              ELSE NULL 
          END) as active_expiry_date
      FROM agent_usage_logs 
      GROUP BY user_id, agent_id
    `;
    
    await executeQuery(createViewQuery);

    return NextResponse.json({
      success: true,
      message: 'agent_usage_logs 表结构更新成功',
      details: {
        addedFields: ['usage_count', 'expiry_date'],
        addedIndexes: ['idx_expiry_date'],
        createdViews: ['user_agent_usage_summary']
      }
    });

  } catch (error) {
    console.error('更新 agent_usage_logs 表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '更新表结构失败',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

import { AIProvider, FileInfo } from '@/types';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { Agent } from '@/types/agent';

interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface AIServiceResponse {
  success: boolean;
  data?: string;
  error?: string;
  usage?: any;
}

interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

interface CozeConfig {
  apiKey: string;
  botId: string;
  userId: string;
}

class AIService {
  private getProviderConfig(provider: AIProvider): ProviderConfig | null {
    try {
      // 使用新的数据库配置系统
      const store = useAIProviderConfigsStore.getState();
      const providerConfig = store.getProviderConfig(provider);

      if (!providerConfig?.enabled || !providerConfig?.apiKey) {
        return null;
      }

      return {
        apiKey: providerConfig.apiKey,
        baseUrl: providerConfig.baseUrl,
        model: providerConfig.defaultModel,
        temperature: providerConfig.temperature,
        maxTokens: providerConfig.maxTokens,
      };
    } catch (error) {
      console.error('Failed to get provider config:', error);
      return null;
    }
  }

  async sendMessage(
    provider: AIProvider,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[],
    agent?: Agent
  ): Promise<AIServiceResponse> {
    // 如果提供了智能体信息，使用Coze API
    if (agent && agent.cozeConfig && agent.cozeConfig.apiKey && agent.cozeConfig.botId) {
      return await this.callCozeAPI(agent.cozeConfig, content, conversationHistory);
    }

    const config = this.getProviderConfig(provider);

    if (!config) {
      return {
        success: false,
        error: `${provider} 服务未配置或未启用，请先在设置中配置 API Key`,
      };
    }

    try {
      // 使用新的统一 API 路由
      return await this.callUnifiedAPI(provider, config, content, files, conversationHistory);
    } catch (error) {
      console.error(`${provider} API call failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  private async callUnifiedAPI(
    provider: AIProvider,
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    // 获取用户当前选择的模型，而不是提供商的默认模型
    const store = useAIProviderConfigsStore.getState();
    const currentModel = store.userSettings?.currentModel || config.model;

    // 使用当前选择的模型覆盖配置中的模型
    const configWithCurrentModel = {
      ...config,
      model: currentModel
    };

    const requestBody = {
      provider,
      config: configWithCurrentModel,
      messages,
      content,
      files,
    };



    const response = await fetch('/api/ai-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.error || `API 请求失败: ${response.status}`,
      };
    }

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: data.error || '未知错误',
      };
    }

    return {
      success: true,
      data: data.data.content,
      usage: data.data.usage,
    };
  }

  private async callGoogleAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    // 处理 baseUrl，兼容标准 API 和代理 API
    let baseUrl = config.baseUrl || 'https://generativelanguage.googleapis.com';

    // 移除末尾的斜杠
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

      // 智能处理 URL 路径，兼容标准 API 和代理 API
    let url;
    let isProxyAPI = false;

    if (baseUrl.includes('/v1beta')) {
      // 如果已经包含 v1beta 路径，直接添加 models 部分
      url = `${baseUrl}/models/${config.model}:generateContent?key=${config.apiKey}`;
    } else if (baseUrl.includes('/v1') && !baseUrl.includes('/v1beta')) {
      // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
      url = `${baseUrl}/chat/completions`;
      isProxyAPI = true;
    } else {
      // 标准情况，添加完整的 v1beta/models 路径
      url = `${baseUrl}/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`;
    }

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    // 根据 API 类型选择不同的请求体格式
    let requestBody;
    let headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (isProxyAPI) {
      // 代理 API 使用 OpenAI 兼容格式
      requestBody = {
        model: config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: config.temperature,
        max_tokens: config.maxTokens,
      };
      headers['Authorization'] = `Bearer ${config.apiKey}`;
    } else {
      // 标准 Google API 格式
      requestBody = {
        contents: messages.map(msg => ({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }]
        })),
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxTokens,
        }
      };
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // 根据 API 类型解析不同的响应格式
    let responseText;
    if (isProxyAPI) {
      // 代理 API 使用 OpenAI 兼容格式
      responseText = data.choices?.[0]?.message?.content;
    } else {
      // 标准 Google API 格式
      responseText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    }

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callOpenAIAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://api.openai.com/v1'}/chat/completions`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      model: config.model,
      messages: messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    };

    console.log('=== OpenAI API 请求详情 ===');
    console.log('URL:', url);
    console.log('Model:', config.model);
    console.log('Request Body:', JSON.stringify(requestBody, null, 2));
    console.log('========================');

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.choices?.[0]?.message?.content;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callAnthropicAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    // 处理 baseUrl，兼容标准 API 和代理 API
    let baseUrl = config.baseUrl || 'https://api.anthropic.com';

    // 移除末尾的斜杠
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    // 智能处理 URL 路径，兼容标准 API 和代理 API
    let url;
    let isProxyAPI = false;

    if (baseUrl.includes('/v1') && !baseUrl.includes('/v1/messages')) {
      // 如果包含 /v1（如代理 API），使用 OpenAI 兼容格式
      url = `${baseUrl}/chat/completions`;
      isProxyAPI = true;
    } else {
      // 标准 Anthropic API 格式
      url = `${baseUrl}/v1/messages`;
    }

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    // 根据 API 类型选择不同的请求体格式
    let requestBody;
    let headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    };

    if (isProxyAPI) {
      // 代理 API 使用 OpenAI 兼容格式
      requestBody = {
        model: config.model,
        messages: messages,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
      };
    } else {
      // 标准 Anthropic API 格式
      requestBody = {
        model: config.model,
        messages: messages,
        max_tokens: config.maxTokens,
        temperature: config.temperature,
      };
      headers['anthropic-version'] = '2023-06-01';
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // 根据 API 类型解析不同的响应格式
    let responseText;
    if (isProxyAPI) {
      // 代理 API 使用 OpenAI 兼容格式
      responseText = data.choices?.[0]?.message?.content;
    } else {
      // 标准 Anthropic API 格式
      responseText = data.content?.[0]?.text;
    }

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callDeepSeekAPI(
    config: ProviderConfig,
    content: string,
    files?: FileInfo[],
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = `${config.baseUrl || 'https://api.deepseek.com'}/v1/chat/completions`;

    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      model: config.model,
      messages: messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.choices?.[0]?.message?.content;

    if (!responseText) {
      throw new Error('API 返回了空响应');
    }

    return {
      success: true,
      data: responseText,
    };
  }

  private async callCozeAPI(
    config: CozeConfig,
    content: string,
    conversationHistory?: ConversationMessage[]
  ): Promise<AIServiceResponse> {
    const url = 'https://api.coze.cn/open_api/v2/chat';

    // 构建消息历史
    const messages = [
      ...(conversationHistory || []),
      { role: 'user', content }
    ];

    const requestBody = {
      conversation_id: `conv_${Date.now()}`,
      bot_id: config.botId,
      user: config.userId,
      query: content,
      chat_history: messages.slice(0, -1).map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content,
        content_type: 'text'
      })),
      stream: false
    };



    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Coze API Error:', errorData);
      throw new Error(errorData.msg || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();


    // 检查响应格式
    if (data.code !== 0) {
      throw new Error(data.msg || 'Coze API 返回错误');
    }

    // 提取回复内容
    const messages_response = data.messages || [];
    const assistantMessage = messages_response.find((msg: any) =>
      msg.role === 'assistant' && msg.type === 'answer'
    );

    if (!assistantMessage || !assistantMessage.content) {
      throw new Error('Coze API 返回了空响应');
    }

    return {
      success: true,
      data: assistantMessage.content,
    };
  }
}

export const aiService = new AIService();
export default aiService;

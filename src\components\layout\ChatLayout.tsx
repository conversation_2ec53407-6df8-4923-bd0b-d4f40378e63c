'use client';

import { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Drawer } from 'antd';
import {
  MessageOutlined,
  BulbOutlined,
  BookOutlined,
  FileOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;

const ChatLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuthStore();
  const { canViewSettings } = usePermissions();

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setCollapsed(true);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const allMenuItems = [
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: '智能对话',
    },
    {
      key: '/agents',
      icon: <BulbOutlined />,
      label: '智能体',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
      requiresPermission: true,
    },
  ];

  // 根据权限过滤菜单项
  const menuItems = allMenuItems.filter(item =>
    !item.requiresPermission || canViewSettings()
  );

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  const toggleMobileMenu = () => {
    if (isMobile) {
      setMobileMenuVisible(!mobileMenuVisible);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const renderSidebarContent = () => (
    <>
      <div className="flex items-center justify-center h-16 border-b border-gray-200">
        {(!collapsed || isMobile) ? (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">G</span>
            </div>
            <span className="font-semibold text-gray-800">Gemini 工具</span>
          </div>
        ) : (
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">G</span>
          </div>
        )}
      </div>

      <Menu
        mode="inline"
        selectedKeys={[pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        className="border-none"
        style={{ height: 'calc(100vh - 64px)' }}
      />
    </>
  );

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="bg-white shadow-lg"
          width={240}
        >
          {renderSidebarContent()}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          title="菜单"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          styles={{ body: { padding: 0 } }}
          width={240}
        >
          {renderSidebarContent()}
        </Drawer>
      )}
      
      <Layout>
        <Header className="bg-white shadow-sm px-3 md:px-6 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              type="text"
              icon={isMobile ? <MenuFoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
              onClick={toggleMobileMenu}
              className="text-lg"
            />
            {isMobile && (
              <span className="font-semibold text-gray-800 text-sm md:text-base">Gemini 工具</span>
            )}
          </div>

          <Space>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 md:px-3 py-2 rounded-lg transition-colors">
                <Avatar size="small" icon={<UserOutlined />} />
                <span className="text-xs md:text-sm font-medium hidden sm:inline">{user?.username}</span>
              </div>
            </Dropdown>
          </Space>
        </Header>
        
        <Content className="p-3 md:p-6 bg-gray-50">
          <div className="h-full bg-white rounded-lg shadow-sm overflow-hidden">
            {/* 这里将根据路由显示不同的内容 */}
            {pathname === '/chat' && <ChatContent />}
            {pathname === '/batch' && <BatchContent />}
            {pathname === '/prompts' && <PromptsContent />}
            {pathname === '/files' && <FilesContent />}
            {pathname === '/settings' && <SettingsContent />}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

// 临时占位组件
const ChatContent = () => (
  <div className="h-full flex items-center justify-center">
    <div className="text-center">
      <MessageOutlined className="text-6xl text-blue-500 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">智能对话</h2>
      <p className="text-gray-600">聊天功能正在开发中...</p>
    </div>
  </div>
);

const BatchContent = () => (
  <div className="h-full flex items-center justify-center">
    <div className="text-center">
      <BulbOutlined className="text-6xl text-green-500 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">批量生成</h2>
      <p className="text-gray-600">批量生成功能正在开发中...</p>
    </div>
  </div>
);

const PromptsContent = () => (
  <div className="h-full flex items-center justify-center">
    <div className="text-center">
      <BookOutlined className="text-6xl text-purple-500 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">提示词管理</h2>
      <p className="text-gray-600">提示词管理功能正在开发中...</p>
    </div>
  </div>
);

const FilesContent = () => (
  <div className="h-full flex items-center justify-center">
    <div className="text-center">
      <FileOutlined className="text-6xl text-indigo-500 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">文件管理</h2>
      <p className="text-gray-600">文件管理功能正在开发中...</p>
    </div>
  </div>
);

const SettingsContent = () => (
  <div className="h-full flex items-center justify-center">
    <div className="text-center">
      <SettingOutlined className="text-6xl text-orange-500 mb-4" />
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">设置</h2>
      <p className="text-gray-600">设置功能正在开发中...</p>
    </div>
  </div>
);

export default ChatLayout;
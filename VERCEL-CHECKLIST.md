# ✅ Vercel 部署检查清单

## 📋 部署前检查

### 1. 代码准备
- [ ] 代码已提交到 Git 仓库
- [ ] 所有功能在本地测试正常
- [ ] 移除了敏感信息和调试代码
- [ ] 更新了 README 和文档

### 2. 云数据库准备
- [ ] 已选择云数据库服务商
- [ ] 获得数据库连接信息
- [ ] 测试数据库连接正常
- [ ] 准备好初始化 SQL 脚本

### 3. 邮件服务准备
- [ ] 配置了 SMTP 邮件服务
- [ ] 测试邮件发送功能
- [ ] 获得 SMTP 认证信息

## 🚀 部署步骤

### 快速部署 (推荐)
```bash
# 1. 生成环境变量配置
npm run generate-env

# 2. 一键部署
npm run setup-vercel
```

### 手动部署
```bash
# 1. 安装 Vercel CLI
npm install -g vercel

# 2. 登录 Vercel
vercel login

# 3. 部署项目
vercel

# 4. 部署到生产环境
vercel --prod
```

## ⚙️ 环境变量配置

在 Vercel 项目设置中添加以下环境变量：

### 必需变量

#### 数据库配置 (二选一)
**方式一：使用 DATABASE_URL (推荐)**
- [ ] `DATABASE_URL` - 完整的数据库连接字符串

**方式二：使用单独的环境变量**
- [ ] `DB_HOST` - 数据库主机地址
- [ ] `DB_PORT` - 数据库端口 (通常是 3306)
- [ ] `DB_USER` - 数据库用户名
- [ ] `DB_PASSWORD` - 数据库密码
- [ ] `DB_NAME` - 数据库名称

#### 其他必需变量
- [ ] `SMTP_HOST` - SMTP 主机
- [ ] `SMTP_PORT` - SMTP 端口
- [ ] `SMTP_USER` - SMTP 用户名
- [ ] `SMTP_PASS` - SMTP 密码
- [ ] `ENCRYPTION_KEY` - 32位加密密钥

### 自动配置变量 (无需手动设置)
- [ ] `NEXT_PUBLIC_API_URL` - API 地址 (自动获取当前域名)

### 可选变量
- [ ] `NODE_ENV=production`
- [ ] `DISABLE_ESLINT_PLUGIN=true`
- [ ] `GENERATE_SOURCEMAP=false`
- [ ] `NODE_NO_WARNINGS=1`

## 🗄️ 数据库初始化

- [ ] 连接到云数据库
- [ ] 执行 `scripts/cloud-db-setup.sql`
- [ ] 验证表结构创建成功
- [ ] 检查默认数据插入

## 🧪 部署后测试

### 基础功能测试
- [ ] 网站可以正常访问
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 邮件验证功能正常

### 核心功能测试
- [ ] 聊天功能正常
- [ ] 模型选择功能正常
- [ ] 批量生成功能正常
- [ ] 设置页面功能正常

### 性能测试
- [ ] 页面加载速度正常
- [ ] API 响应时间正常
- [ ] 数据库查询性能正常

## 🔧 常见问题排查

### 数据库连接问题
- [ ] 检查数据库连接信息
- [ ] 确认数据库允许外部连接
- [ ] 检查网络和防火墙设置

### 环境变量问题
- [ ] 确认所有必需变量已设置
- [ ] 检查变量名称拼写
- [ ] 重新部署使变量生效

### 邮件发送问题
- [ ] 检查 SMTP 配置
- [ ] 确认邮箱服务商设置
- [ ] 测试邮件服务连接

### 函数超时问题
- [ ] 优化代码性能
- [ ] 检查数据库查询效率
- [ ] 考虑升级 Vercel 套餐

## 📊 监控和维护

### 部署后监控
- [ ] 设置 Vercel Analytics
- [ ] 配置错误监控
- [ ] 设置性能监控

### 定期维护
- [ ] 定期备份数据库
- [ ] 更新依赖包
- [ ] 监控资源使用情况
- [ ] 检查安全漏洞

## 🎯 优化建议

### 性能优化
- [ ] 启用 CDN 加速
- [ ] 优化图片和静态资源
- [ ] 使用缓存策略

### 安全优化
- [ ] 使用强密码
- [ ] 定期更换密钥
- [ ] 启用 HTTPS
- [ ] 配置安全头

### 用户体验优化
- [ ] 配置自定义域名
- [ ] 设置错误页面
- [ ] 优化移动端体验

---

✅ **检查完成后，你的微甜 AI Studio 就可以成功部署到 Vercel 了！**

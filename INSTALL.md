# Gemini AI Chat Tool - Next.js 版本安装指南

## 项目概述

这是一个基于 Next.js 15 + TypeScript 的现代化 Gemini AI 聊天工具，采用 lobe-chat 的设计风格，提供智能对话、批量生成、提示词管理等功能。

## 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Ant Design + antd-style + Tailwind CSS
- **状态管理**: Zustand
- **图标**: Lucide React
- **API**: Google Gemini AI

## 安装步骤

### 1. 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 2. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置

创建 `.env.local` 文件并配置以下环境变量：

```env
# Gemini API 配置
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here
NEXT_PUBLIC_GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent

# 应用配置
NEXT_PUBLIC_APP_NAME=Gemini AI Chat Tool
NEXT_PUBLIC_APP_VERSION=2.0.0
```

### 4. 启动开发服务器

```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

应用将在 `http://localhost:3000` 启动。

### 5. 构建生产版本

```bash
# 构建
npm run build

# 启动生产服务器
npm run start
```

## 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── login/             # 登录页面
│   ├── chat/              # 聊天界面
│   ├── batch/             # 批量生成
│   ├── prompts/           # 提示词管理
│   ├── settings/          # 设置页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── layout/           # 布局组件
│   ├── chat/             # 聊天相关组件
│   ├── ui/               # UI 基础组件
│   └── common/           # 通用组件
├── store/                # Zustand 状态管理
├── styles/               # 样式配置
├── types/                # TypeScript 类型定义
├── utils/                # 工具函数
└── lib/                  # 第三方库配置
```

## 功能特性

### ✅ 已完成功能

- [x] 现代化 UI 设计（基于 lobe-chat 风格）
- [x] 用户认证系统
- [x] 响应式布局
- [x] TypeScript 类型安全
- [x] 主题配置系统
- [x] 路由保护

### 🚧 开发中功能

- [ ] 智能聊天界面
- [ ] 消息组件和历史记录
- [ ] 文件上传功能
- [ ] 批量文本生成
- [ ] 提示词管理
- [ ] 设置面板
- [ ] API 集成
- [ ] 导出功能

## 开发指南

### 添加新页面

1. 在 `src/app/` 目录下创建新的路由文件夹
2. 添加 `page.tsx` 文件
3. 在 `ChatLayout.tsx` 中添加导航链接

### 添加新组件

1. 在 `src/components/` 相应目录下创建组件文件
2. 使用 TypeScript 定义 props 接口
3. 导出组件供其他文件使用

### 状态管理

使用 Zustand 进行状态管理：

```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface MyStore {
  // 状态定义
}

export const useMyStore = create<MyStore>()(
  persist(
    (set, get) => ({
      // 状态实现
    }),
    {
      name: 'my-store',
    }
  )
);
```

### 样式开发

项目使用多层样式系统：

1. **Ant Design**: 基础组件库
2. **antd-style**: 动态样式和主题
3. **Tailwind CSS**: 工具类样式

```typescript
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    background: ${token.colorBgContainer};
    padding: ${token.padding}px;
  `,
}));
```

## 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript 错误**
   ```bash
   npm run type-check
   ```

3. **样式不生效**
   - 检查 Tailwind CSS 配置
   - 确认 antd-style 主题设置

4. **API 调用失败**
   - 检查 `.env.local` 配置
   - 验证 Gemini API 密钥

### 开发工具

推荐使用以下 VS Code 扩展：

- TypeScript Importer
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Auto Rename Tag
- Prettier - Code formatter

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件
- 提交 Pull Request

---

**注意**: 这是一个迁移项目，从原始的 HTML/CSS/JavaScript 版本升级到现代化的 Next.js + TypeScript 架构。所有原有功能都将在新版本中重新实现并增强。
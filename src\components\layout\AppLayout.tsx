'use client';

import React from 'react';
import { Flexbox } from 'react-layout-kit';
import { createStyles } from 'antd-style';
import MainSideBar from './MainSideBar';

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;
  `,
}));

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { styles } = useStyles();

  return (
    <Flexbox
      className={styles.layout}
      height={'100%'}
      horizontal
      width={'100%'}
    >
      {/* 最左边的主导航侧边栏 */}
      <MainSideBar />
      
      {/* 主内容区域 */}
      <Flexbox flex={1} style={{ overflow: 'hidden' }}>
        {children}
      </Flexbox>
    </Flexbox>
  );
};

export default AppLayout;

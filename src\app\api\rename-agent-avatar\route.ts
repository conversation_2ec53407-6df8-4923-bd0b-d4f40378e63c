import { NextRequest, NextResponse } from 'next/server';
import { rename, unlink, readdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const { tempId, newAgentId } = await request.json();

    if (!tempId || !newAgentId) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    const uploadsDir = join(process.cwd(), 'public', 'agents_shortcut');

    // 读取目录中的所有文件
    const files = await readdir(uploadsDir);

    // 查找以tempId开头的文件
    const tempFile = files.find(file => file.startsWith(tempId + '.'));

    if (!tempFile) {
      return NextResponse.json({ error: '未找到临时文件' }, { status: 404 });
    }

    const fileExtension = tempFile.split('.').pop();

    const oldPath = join(uploadsDir, tempFile);
    const newFileName = `${newAgentId}.${fileExtension}`;
    const newPath = join(uploadsDir, newFileName);

    // 如果目标文件已存在，先删除
    if (existsSync(newPath)) {
      await unlink(newPath);
    }

    // 重命名文件
    await rename(oldPath, newPath);

    // 返回新的相对路径
    const newRelativePath = `/agents_shortcut/${newFileName}`;

    return NextResponse.json({
      success: true,
      url: newRelativePath,
      message: '文件重命名成功'
    });

  } catch (error) {
    console.error('重命名文件失败:', error);
    return NextResponse.json({
      error: '重命名失败'
    }, { status: 500 });
  }
}

'use client';

import { ActionIcon, ActionIconProps } from '@lobehub/ui';
import { MessageSquare, Layers, FileText, Settings, Bot } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';
import { usePermissions } from '@/hooks/usePermissions';

const ICON_SIZE: ActionIconProps['size'] = {
  blockSize: 40,
  size: 24,
  strokeWidth: 2,
};

const MainSideBarTopActions = memo(() => {
  const pathname = usePathname();
  const { canViewSettings } = usePermissions();

  const isChatActive = pathname === '/chat';
  const isAgentsActive = pathname === '/agents';
  const isSettingsActive = pathname.startsWith('/settings');

  return (
    <Flexbox gap={8}>
      <Link aria-label="智能对话" href="/chat">
        <ActionIcon
          active={isChatActive}
          icon={MessageSquare}
          size={ICON_SIZE}
          title="智能对话"
          tooltipProps={{ placement: 'right' }}
        />
      </Link>

      <Link aria-label="智能体" href="/agents">
        <ActionIcon
          active={isAgentsActive}
          icon={Bot}
          size={ICON_SIZE}
          title="智能体"
          tooltipProps={{ placement: 'right' }}
        />
      </Link>

      {canViewSettings() && (
        <Link aria-label="设置" href="/settings">
          <ActionIcon
            active={isSettingsActive}
            icon={Settings}
            size={ICON_SIZE}
            title="设置"
            tooltipProps={{ placement: 'right' }}
          />
        </Link>
      )}
    </Flexbox>
  );
});

MainSideBarTopActions.displayName = 'MainSideBarTopActions';

export default MainSideBarTopActions;

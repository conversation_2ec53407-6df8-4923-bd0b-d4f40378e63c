import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// GET - 导出兑换码Excel
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    console.log('Excel导出请求 - batchId:', batchId);

    if (!batchId) {
      console.log('Excel导出失败 - 缺少batchId');
      return NextResponse.json(
        {
          success: false,
          error: '批次ID为必填项',
        },
        { status: 400 }
      );
    }

    // 获取批次信息
    const batchQuery = `
      SELECT rb.*, a.name as agent_name
      FROM redemption_batches rb
      LEFT JOIN agents a ON rb.agent_id = a.id
      WHERE rb.id = ?
    `;
    
    const batchResults = await executeQuery<any[]>(batchQuery, [batchId]);
    
    if (batchResults.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '批次不存在',
        },
        { status: 404 }
      );
    }

    const batch = batchResults[0];

    // 获取兑换码列表
    const codesQuery = `
      SELECT * FROM redemption_codes
      WHERE batch_id = ?
      ORDER BY created_at ASC
    `;
    
    const codes = await executeQuery<any[]>(codesQuery, [batchId]);

    console.log('兑换码数据获取成功，数量:', codes.length);
    console.log('批次信息:', batch.batch_name, batch.label);

    // 准备兑换码列表数据
    const codesData = [
      ['兑换码', '类型', '标签', '价值', '有效期', '状态', '使用者', '使用时间', '创建时间'],
      ...codes.map(code => [
        code.code,
        code.code_type === 'duration' ? '时长兑换' : '次数兑换',
        code.label,
        code.code_type === 'duration'
          ? `${code.duration_days}天`
          : `${code.usage_count}次`,
        code.expires_at ? new Date(code.expires_at).toLocaleDateString() : '永久有效',
        code.used_at ? '已使用' : '未使用',
        code.used_by || '',
        code.used_at ? new Date(code.used_at).toLocaleString() : '',
        new Date(code.created_at).toLocaleString()
      ])
    ];

    // 准备使用说明数据
    const instructionsData = [
      ['兑换码使用说明'],
      [''],
      ['1. 兑换码信息'],
      [`   智能体: ${batch.agent_name}`],
      [`   批次名称: ${batch.batch_name}`],
      [`   兑换类型: ${batch.code_type === 'duration' ? '时长兑换' : '次数兑换'}`],
      [`   兑换价值: ${batch.code_type === 'duration' ? `${batch.duration_days}天` : `${batch.usage_count}次`}`],
      [`   总数量: ${batch.total_count}个`],
      [`   已使用: ${batch.used_count}个`],
      [`   剩余: ${batch.total_count - batch.used_count}个`],
      [`   有效期: ${batch.expires_at ? new Date(batch.expires_at).toLocaleDateString() : '永久有效'}`],
      [`   创建时间: ${new Date(batch.created_at).toLocaleString()}`],
      [''],
      ['2. 使用方法'],
      ['   - 每个兑换码只能使用一次'],
      ['   - 兑换码格式：RC-XXXX-XXXX'],
      ['   - 请妥善保管兑换码，避免泄露'],
      ['   - 如有问题请联系客服'],
      [''],
      ['3. 注意事项'],
      ['   - 兑换码一旦使用无法撤销'],
      ['   - 过期的兑换码无法使用'],
      ['   - 每个用户在同一批次中只能使用一个兑换码'],
      ['   - 兑换获得的权益会自动添加到用户账户'],
      [''],
      ['4. 技术支持'],
      ['   如遇到兑换问题，请提供兑换码和错误信息联系技术支持'],
    ];

    // 准备统计信息数据
    const statsData = [
      ['兑换码统计信息'],
      [''],
      ['项目', '数值'],
      ['总兑换码数量', batch.total_count],
      ['已使用数量', batch.used_count],
      ['未使用数量', batch.total_count - batch.used_count],
      ['使用率', `${((batch.used_count / batch.total_count) * 100).toFixed(2)}%`],
      ['创建日期', new Date(batch.created_at).toLocaleDateString()],
      ['兑换类型', batch.code_type === 'duration' ? '时长兑换' : '次数兑换'],
      ['兑换价值', batch.code_type === 'duration' ? `${batch.duration_days}天` : `${batch.usage_count}次`],
      ['有效期', batch.expires_at ? new Date(batch.expires_at).toLocaleDateString() : '永久有效'],
    ];

    // 生成文件名
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `兑换码_${batch.label}_${timestamp}.xlsx`;

    // 返回Excel数据给前端，让前端用xlsx库生成文件
    return NextResponse.json({
      success: true,
      data: {
        codes: codesData,
        instructions: instructionsData,
        stats: statsData,
        filename: filename,
        // 列宽配置
        colWidths: {
          codes: [
            { width: 20 }, // 兑换码
            { width: 12 }, // 类型
            { width: 15 }, // 标签
            { width: 10 }, // 价值
            { width: 12 }, // 有效期
            { width: 10 }, // 状态
            { width: 15 }, // 使用者
            { width: 20 }, // 使用时间
            { width: 20 }, // 创建时间
          ],
          instructions: [{ width: 60 }],
          stats: [{ width: 20 }, { width: 20 }]
        }
      }
    });

  } catch (error) {
    console.error('导出兑换码Excel失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '导出兑换码Excel失败',
      },
      { status: 500 }
    );
  }
}

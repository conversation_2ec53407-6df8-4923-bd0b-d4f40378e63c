// 文件上传和处理服务

export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  content?: string;
  uploadTime: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export interface FileUploadOptions {
  maxSize?: number; // 最大文件大小（字节）
  allowedTypes?: string[]; // 允许的文件类型
  maxFiles?: number; // 最大文件数量
}

class FileUploadService {
  private static instance: FileUploadService;
  private uploadedFiles: Map<string, FileInfo> = new Map();

  static getInstance(): FileUploadService {
    if (!FileUploadService.instance) {
      FileUploadService.instance = new FileUploadService();
    }
    return FileUploadService.instance;
  }

  // 默认配置
  private defaultOptions: FileUploadOptions = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'text/plain',
      'text/csv',
      'application/json',
      'text/markdown',
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
    ],
    maxFiles: 10,
  };

  // 生成文件ID
  private generateFileId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 验证文件
  private validateFile(file: File, options: FileUploadOptions): string | null {
    const opts = { ...this.defaultOptions, ...options };

    // 检查文件大小
    if (opts.maxSize && file.size > opts.maxSize) {
      return `文件大小超过限制 (${this.formatFileSize(opts.maxSize)})`;
    }

    // 检查文件类型
    if (opts.allowedTypes && !opts.allowedTypes.includes(file.type)) {
      return `不支持的文件类型: ${file.type}`;
    }

    return null;
  }

  // 格式化文件大小
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 读取文本文件内容
  private async readTextFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve(content);
      };
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'UTF-8');
    });
  }

  // 读取图片文件为 Data URL
  private async readImageFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        resolve(dataUrl);
      };
      reader.onerror = () => reject(new Error('图片读取失败'));
      reader.readAsDataURL(file);
    });
  }

  // 上传单个文件
  async uploadFile(file: File, options: FileUploadOptions = {}): Promise<FileInfo> {
    const fileId = this.generateFileId();
    
    // 创建文件信息
    const fileInfo: FileInfo = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      uploadTime: Date.now(),
      status: 'uploading',
    };

    // 验证文件
    const validationError = this.validateFile(file, options);
    if (validationError) {
      fileInfo.status = 'error';
      fileInfo.error = validationError;
      this.uploadedFiles.set(fileId, fileInfo);
      throw new Error(validationError);
    }

    try {
      // 根据文件类型处理
      if (file.type.startsWith('text/') || file.type === 'application/json') {
        // 文本文件
        const content = await this.readTextFile(file);
        fileInfo.content = content;
      } else if (file.type.startsWith('image/')) {
        // 图片文件
        const dataUrl = await this.readImageFile(file);
        fileInfo.url = dataUrl;
      } else if (file.type === 'application/pdf') {
        // PDF 文件 - 暂时只存储文件信息
        fileInfo.content = ''; // PDF 内容提取需要额外的库
      }

      fileInfo.status = 'success';
      this.uploadedFiles.set(fileId, fileInfo);
      return fileInfo;
    } catch (error) {
      fileInfo.status = 'error';
      fileInfo.error = error instanceof Error ? error.message : '上传失败';
      this.uploadedFiles.set(fileId, fileInfo);
      throw error;
    }
  }

  // 批量上传文件
  async uploadFiles(files: File[], options: FileUploadOptions = {}): Promise<FileInfo[]> {
    const opts = { ...this.defaultOptions, ...options };
    
    // 检查文件数量限制
    if (opts.maxFiles && files.length > opts.maxFiles) {
      throw new Error(`文件数量超过限制 (${opts.maxFiles})`);
    }

    const results: FileInfo[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {
        const fileInfo = await this.uploadFile(file, options);
        results.push(fileInfo);
      } catch (error) {
        errors.push(`${file.name}: ${error instanceof Error ? error.message : '上传失败'}`);
      }
    }

    if (errors.length > 0) {
      console.warn('部分文件上传失败:', errors);
    }

    return results;
  }

  // 获取文件信息
  getFileInfo(fileId: string): FileInfo | undefined {
    return this.uploadedFiles.get(fileId);
  }

  // 获取所有文件
  getAllFiles(): FileInfo[] {
    return Array.from(this.uploadedFiles.values());
  }

  // 删除文件
  deleteFile(fileId: string): boolean {
    return this.uploadedFiles.delete(fileId);
  }

  // 清空所有文件
  clearFiles(): void {
    this.uploadedFiles.clear();
  }

  // 获取文件内容用于 AI 处理
  getFileContentForAI(fileId: string): string | null {
    const fileInfo = this.getFileInfo(fileId);
    if (!fileInfo || fileInfo.status !== 'success') {
      return null;
    }

    if (fileInfo.content) {
      return fileInfo.content;
    }

    if (fileInfo.url && fileInfo.type.startsWith('image/')) {
      return `[图片文件: ${fileInfo.name}]`;
    }

    return `[文件: ${fileInfo.name}, 类型: ${fileInfo.type}]`;
  }

  // 导出文件列表
  exportFileList(): string {
    const files = this.getAllFiles();
    const fileList = files.map(file => ({
      name: file.name,
      size: this.formatFileSize(file.size),
      type: file.type,
      uploadTime: new Date(file.uploadTime).toLocaleString(),
      status: file.status,
    }));

    return JSON.stringify(fileList, null, 2);
  }
}

// 导出单例实例
export const fileUploadService = FileUploadService.getInstance();
export default fileUploadService;

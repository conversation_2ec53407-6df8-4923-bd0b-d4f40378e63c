import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { Prompt } from '@/types';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// 生成唯一ID
const generateId = () => Math.random().toString(36).substring(2, 11);

// OPTIONS - 处理预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

// GET - 获取所有提示词
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const tags = searchParams.get('tags');

    let query = 'SELECT * FROM prompts WHERE 1=1';
    const params: any[] = [];

    // 分类筛选
    if (category && category !== 'all') {
      query += ' AND category = ?';
      params.push(category);
    }

    // 搜索筛选
    if (search) {
      query += ' AND (title LIKE ? OR description LIKE ? OR content LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // 标签筛选
    if (tags) {
      const tagArray = tags.split(',');
      for (const tag of tagArray) {
        query += ' AND JSON_CONTAINS(tags, ?)';
        params.push(JSON.stringify(tag));
      }
    }

    query += ' ORDER BY created_at DESC';

    const prompts = await executeQuery<any[]>(query, params);

    // 转换数据格式
    const formattedPrompts: Prompt[] = prompts.map(prompt => {
      let tags = [];

      // 处理tags字段 - MySQL JSON类型可能返回数组或字符串
      if (prompt.tags) {
        if (Array.isArray(prompt.tags)) {
          tags = prompt.tags;
        } else if (typeof prompt.tags === 'string') {
          try {
            tags = JSON.parse(prompt.tags);
          } catch (error) {
            console.error('解析tags失败:', prompt.tags, error);
            tags = [];
          }
        }
      }

      return {
        id: prompt.id,
        title: prompt.title,
        content: prompt.content,
        description: '', // 数据库表中没有此字段
        category: prompt.category,
        tags,
        isFavorite: Boolean(prompt.is_favorite),
        isPublic: true, // 数据库表中没有此字段，设为默认值
        createdAt: prompt.created_at.toISOString(),
        updatedAt: prompt.updated_at.toISOString(),
      };
    });

    return NextResponse.json({
      success: true,
      data: formattedPrompts,
    }, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('获取提示词失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取提示词失败',
      },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// POST - 创建新提示词
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, content, category, tags, isFavorite } = body;

    if (!title || !content) {
      return NextResponse.json(
        {
          success: false,
          error: '标题和内容为必填项',
        },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    const id = generateId();
    const now = new Date();

    const query = `
      INSERT INTO prompts (id, title, content, category, tags, is_favorite, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      id,
      title,
      content,
      category || 'custom',
      tags ? JSON.stringify(tags) : null,
      Boolean(isFavorite),
      now,
      now,
    ];

    await executeQuery(query, params);

    const newPrompt: Prompt = {
      id,
      title: title || '',
      content: content || '',
      description: '', // 数据库表中没有此字段，设为空字符串
      category: category || 'custom',
      tags: tags || [],
      isFavorite: Boolean(isFavorite),
      isPublic: true, // 数据库表中没有此字段，设为默认值
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: newPrompt,
    }, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('创建提示词失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '创建提示词失败',
      },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// PUT - 更新提示词
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, content, category, tags, isFavorite } = body;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '提示词ID为必填项',
        },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    if (!title || !content) {
      return NextResponse.json(
        {
          success: false,
          error: '标题和内容为必填项',
        },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    const query = `
      UPDATE prompts
      SET title = ?, content = ?, category = ?, tags = ?,
          is_favorite = ?, updated_at = ?
      WHERE id = ?
    `;

    const params = [
      title,
      content,
      category || 'custom',
      tags ? JSON.stringify(tags) : null,
      Boolean(isFavorite),
      new Date(),
      id,
    ];

    const result = await executeQuery<any>(query, params);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '提示词不存在',
        },
        {
          status: 404,
          headers: corsHeaders,
        }
      );
    }

    return NextResponse.json({
      success: true,
      message: '提示词更新成功',
    }, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('更新提示词失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '更新提示词失败',
      },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// DELETE - 删除提示词
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: '提示词ID为必填项',
        },
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    const query = 'DELETE FROM prompts WHERE id = ?';
    const result = await executeQuery<any>(query, [id]);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '提示词不存在',
        },
        {
          status: 404,
          headers: corsHeaders,
        }
      );
    }

    return NextResponse.json({
      success: true,
      message: '提示词删除成功',
    }, {
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('删除提示词失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '删除提示词失败',
      },
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
}

// 测试使用次数累加功能
const fetch = require('node-fetch');

async function testUsageTracking() {
  const testData = {
    userId: 'mdoi3uqorptmrc7zwr',
    agentId: 'mdpk4ezijzy5fxs26v',
    sessionId: 'test_session_' + Date.now(),
    usageType: 'chat',
    tokensUsed: 0,
    cost: 0,
    usageCount: 1,
    expiryDate: null
  };

  console.log('测试数据:', testData);

  try {
    // 发送第一次请求
    console.log('\n=== 第一次调用 ===');
    const response1 = await fetch('http://localhost:3000/api/agent-usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result1 = await response1.json();
    console.log('第一次调用结果:', result1);

    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 发送第二次请求
    console.log('\n=== 第二次调用 ===');
    const response2 = await fetch('http://localhost:3000/api/agent-usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result2 = await response2.json();
    console.log('第二次调用结果:', result2);

    // 查询结果
    console.log('\n=== 查询使用统计 ===');
    const queryResponse = await fetch(`http://localhost:3000/api/agent-usage?userId=${testData.userId}&agentId=${testData.agentId}`);
    const queryResult = await queryResponse.json();
    console.log('查询结果:', queryResult);

  } catch (error) {
    console.error('测试失败:', error);
  }
}

testUsageTracking();

'use client';

import { memo } from 'react';
import { createStyles } from 'antd-style';
import { Card, Typography, Space, Tag } from 'antd';
import { InfoCircleOutlined, BulbOutlined } from '@ant-design/icons';
import { Agent } from '@/types/agent';

const { Text, Paragraph } = Typography;

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    margin-bottom: 16px;
    border-radius: 8px;
    border: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorFillAlter};
  `,
  
  header: css`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: ${token.colorTextSecondary};
    font-weight: 500;
  `,
  
  content: css`
    .ant-typography {
      margin-bottom: 8px !important;
      
      &:last-child {
        margin-bottom: 0 !important;
      }
    }
  `,
  
  presetPrompts: css`
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  `,
  
  promptTag: css`
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  `,
}));

interface AgentUsageInstructionsProps {
  agent: Agent;
  onPromptClick?: (prompt: string) => void;
}

const AgentUsageInstructions = memo<AgentUsageInstructionsProps>(({ 
  agent, 
  onPromptClick 
}) => {
  const { styles } = useStyles();

  const handlePromptClick = (prompt: string) => {
    if (onPromptClick) {
      onPromptClick(prompt);
    }
  };

  return (
    <Card 
      className={styles.container}
      size="small"
      bodyStyle={{ padding: '16px' }}
    >
      <div className={styles.header}>
        <InfoCircleOutlined />
        <Text strong>使用说明</Text>
      </div>
      
      <div className={styles.content}>
        {agent.usageInstructions && (
          <Paragraph style={{ fontSize: '14px' }}>
            {agent.usageInstructions}
          </Paragraph>
        )}
        
        {agent.presetPrompts && agent.presetPrompts.length > 0 && (
          <Space direction="vertical" size={4} style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <BulbOutlined style={{ color: '#faad14' }} />
              <Text strong style={{ fontSize: '13px' }}>
                推荐提示词：
              </Text>
            </div>
            <div className={styles.presetPrompts}>
              {agent.presetPrompts.map((prompt, index) => (
                <Tag
                  key={index}
                  className={styles.promptTag}
                  color="blue"
                  onClick={() => handlePromptClick(prompt)}
                >
                  {prompt}
                </Tag>
              ))}
            </div>
          </Space>
        )}
      </div>
    </Card>
  );
});

AgentUsageInstructions.displayName = 'AgentUsageInstructions';

export default AgentUsageInstructions;

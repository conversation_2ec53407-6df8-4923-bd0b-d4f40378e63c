-- 修复智能体表中preset_prompts字段的JSON格式
-- 将错误的JavaScript数组格式转换为正确的JSON格式
-- 执行时间: 2024-01-XX

-- 备份当前数据（可选）
-- CREATE TABLE agents_backup AS SELECT * FROM agents WHERE preset_prompts IS NOT NULL;

-- 查看当前有问题的数据
SELECT id, name, preset_prompts 
FROM agents 
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts != 'null' 
  AND preset_prompts != '[]'
  AND preset_prompts != '""';

-- 修复单引号为双引号的问题
-- 这个查询会将 [ 'text' ] 格式转换为 [ "text" ] 格式
UPDATE agents 
SET preset_prompts = REPLACE(REPLACE(preset_prompts, "[ '", '[ "'), "' ]", '" ]')
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts LIKE "%[ '%"
  AND preset_prompts LIKE "%' ]%";

-- 修复中间的单引号
-- 这个查询会将 [ "text1", 'text2' ] 格式转换为 [ "text1", "text2" ] 格式
UPDATE agents 
SET preset_prompts = REPLACE(REPLACE(preset_prompts, "', '", '", "'), "', \"", '", "')
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts LIKE "%',%";

-- 修复剩余的单引号问题
UPDATE agents 
SET preset_prompts = REPLACE(preset_prompts, "'", '"')
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts LIKE "%'%"
  AND preset_prompts NOT LIKE '%"%';

-- 验证修复结果
SELECT id, name, preset_prompts,
       CASE 
         WHEN JSON_VALID(preset_prompts) = 1 THEN '✓ 有效JSON'
         ELSE '✗ 无效JSON'
       END as json_status
FROM agents 
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts != 'null';

-- 清理明显无效的数据
UPDATE agents 
SET preset_prompts = '[]'
WHERE preset_prompts IS NOT NULL 
  AND preset_prompts NOT LIKE '[%]'
  AND preset_prompts != 'null';

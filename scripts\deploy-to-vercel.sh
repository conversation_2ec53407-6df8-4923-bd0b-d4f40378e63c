#!/bin/bash

# Vercel 部署脚本
# 使用方法: ./scripts/deploy-to-vercel.sh

echo "🚀 开始部署到 Vercel..."

# 检查是否安装了 Vercel CLI
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI 未安装，正在安装..."
    npm install -g vercel
fi

# 检查是否已登录
echo "🔐 检查 Vercel 登录状态..."
if ! vercel whoami &> /dev/null; then
    echo "📝 请登录 Vercel..."
    vercel login
fi

# 构建检查
echo "🔍 检查项目构建..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi

echo "✅ 构建成功"

# 部署到预览环境
echo "🌐 部署到预览环境..."
vercel

# 询问是否部署到生产环境
read -p "🤔 是否部署到生产环境? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 部署到生产环境..."
    vercel --prod
    echo "✅ 生产环境部署完成!"
else
    echo "ℹ️  仅部署到预览环境"
fi

echo "🎉 部署完成!"
echo "📋 请确保在 Vercel 控制台中配置了以下环境变量:"
echo "   - DB_HOST"
echo "   - DB_PORT"
echo "   - DB_USER"
echo "   - DB_PASSWORD"
echo "   - DB_NAME"
echo "   - SMTP_HOST"
echo "   - SMTP_PORT"
echo "   - SMTP_USER"
echo "   - SMTP_PASS"
echo "   - ENCRYPTION_KEY"
echo ""
echo "✅ NEXT_PUBLIC_API_URL 已自动配置，无需手动设置"

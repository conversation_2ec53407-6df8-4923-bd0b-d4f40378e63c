'use client';

import React, { memo, useState } from 'react';
import { Flexbox } from 'react-layout-kit';
import { createStyles } from 'antd-style';
import { Button, Typography, App, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { Plus, Edit3, MessageSquare, Trash2, MoreHorizontal, DollarSign } from 'lucide-react';
import { useChatStore, DEFAULT_AGENT_ID } from '@/store/chat';
import { agentStatusApi } from '@/lib/api';
import AgentPricingModal from './AgentPricingModal';

const { Text } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    width: 280px;
    height: 100%;
    background: ${token.colorBgContainer};
    border-right: 1px solid ${token.colorBorderSecondary};
    display: flex;
    flex-direction: column;
  `,
  
  header: css`
    padding: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: ${token.colorText};
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }
  `,
  
  content: css`
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  `,

  sessionsList: css`
    display: flex;
    flex-direction: column;
    gap: 12px;
  `,

  currentSession: css`
    padding: 16px;
    border-radius: 12px;
    background: ${token.colorFillTertiary};
    border: 1px solid ${token.colorBorderSecondary};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: ${token.colorPrimary};
      background: ${token.colorPrimaryBg};

      .session-actions {
        opacity: 1;
      }
    }

    .session-title {
      font-size: 14px;
      font-weight: 600;
      color: ${token.colorText};
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .session-actions {
      display: flex;
      align-items: center;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .session-info {
      font-size: 12px;
      color: ${token.colorTextSecondary};
      margin-bottom: 4px;
    }

    .session-preview {
      font-size: 12px;
      color: ${token.colorTextTertiary};
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  `,

  activeSession: css`
    border-color: ${token.colorPrimary};
    background: ${token.colorPrimaryBg};

    .session-title {
      color: ${token.colorPrimary};
    }
  `,
  
  newSessionButton: css`
    width: 100%;
    height: 48px;
    border: 2px dashed ${token.colorBorder};
    background: transparent;
    color: ${token.colorTextSecondary};
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: ${token.colorPrimary};
      color: ${token.colorPrimary};
      background: ${token.colorPrimaryBg};
    }
  `,
  
  emptyState: css`
    text-align: center;
    padding: 40px 20px;
    
    .empty-icon {
      font-size: 48px;
      color: ${token.colorTextQuaternary};
      margin-bottom: 16px;
    }
    
    .empty-title {
      color: ${token.colorTextSecondary};
      margin-bottom: 8px;
      font-size: 14px;
    }
    
    .empty-description {
      color: ${token.colorTextTertiary};
      font-size: 12px;
      line-height: 1.4;
    }
  `,
}));

const CurrentSessionPanel: React.FC = memo(() => {
  const { styles } = useStyles();
  const { modal, message } = App.useApp();
  const [pricingModalVisible, setPricingModalVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<{ id: string; name: string } | null>(null);

  const {
    currentSessions,
    activeSessionId,
    createSession,
    updateSessionTitle,
    deleteSession,
    checkAgentStatus
  } = useChatStore();

  const handleCreateSession = () => {
    createSession();
    message.success('新会话创建成功');
  };

  // 获取会话显示标题
  const getSessionDisplayTitle = (session: any): string => {
    // 如果是智能体会话且有智能体名称，优先显示智能体名称
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID && session.agentName) {
      return session.agentName;
    }

    // 如果用户已经自定义了标题，显示自定义标题
    if (session.title && session.title !== '新会话' && session.title !== '新对话') {
      return session.title;
    }

    // 否则显示默认标题
    return session.title || '新会话';
  };

  const handleSessionClick = async (session: any) => {
    // 直接切换会话，不阻止用户访问
    useChatStore.setState({ activeSessionId: session.id });

    // 如果是智能体会话，检查状态并显示提示（但不阻止切换）
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      try {
        const result = await agentStatusApi.checkStatus(session.agentId);

        if (result.success && result.data) {
          if (!result.data.exists) {
            message.warning(`智能体"${result.data.name || '未知'}"已不存在，无法发送消息`);
          } else if (!result.data.enabled) {
            message.warning(`智能体"${result.data.name || '未知'}"已被禁用，无法发送消息`);
          }
        }
      } catch (error) {
        console.error('检查智能体状态失败:', error);
        // 静默处理错误，不影响用户体验
      }
    }
  };

  const handleRenameSession = (session: any) => {
    const modalInstance = modal.confirm({
      title: '重命名会话',
      content: (
        <input
          id="current-session-title-input"
          defaultValue={getSessionDisplayTitle(session)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            fontSize: '14px',
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const input = e.target as HTMLInputElement;
              const newTitle = input.value.trim();
              if (newTitle && newTitle !== session.title) {
                updateSessionTitle(session.sessionUniqueId, newTitle);
                message.success('会话重命名成功');
              }
              modalInstance.destroy();
            }
          }}
        />
      ),
      onOk: () => {
        const input = document.getElementById('current-session-title-input') as HTMLInputElement;
        const newTitle = input?.value.trim();
        if (newTitle && newTitle !== session.title) {
          updateSessionTitle(session.sessionUniqueId, newTitle);
          message.success('会话重命名成功');
        }
      },
      okText: '确认',
      cancelText: '取消',
    });

    // 自动聚焦输入框
    setTimeout(() => {
      const input = document.getElementById('current-session-title-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  };

  const handleDeleteSession = (session: any, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    modal.confirm({
      title: '删除会话',
      content: `确定要删除会话"${getSessionDisplayTitle(session)}"吗？此操作不可撤销。`,
      okText: '删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        deleteSession(session.sessionUniqueId);
        message.success('会话删除成功');
      },
    });
  };

  const handleShowPricing = (session: any, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    // 检查是否是智能体会话
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      setSelectedAgent({
        id: session.agentId,
        name: session.agentName || '智能体'
      });
      setPricingModalVisible(true);
    } else {
      message.info('当前会话不是智能体会话，无需查看定价信息');
    }
  };

  const handleMenuClick = (key: string, session: any, e?: any) => {
    switch (key) {
      case 'pricing':
        handleShowPricing(session, e);
        break;
      case 'rename':
        handleRenameSession(session);
        break;
      case 'delete':
        handleDeleteSession(session, e);
        break;
    }
  };

  const getSessionMenuItems = (session: any): MenuProps['items'] => {
    const items: MenuProps['items'] = [
      {
        key: 'rename',
        label: '重命名',
        icon: <Edit3 size={14} />,
      },
      {
        key: 'delete',
        label: '删除',
        icon: <Trash2 size={14} />,
        danger: true,
      },
    ];

    // 如果是智能体会话，添加定价选项
    if (session.agentId && session.agentId !== DEFAULT_AGENT_ID) {
      items.unshift({
        key: 'pricing',
        label: '定价',
        icon: <DollarSign size={14} />,
      });
    }

    return items;
  };

  const getSessionPreview = (session: any) => {
    if (!session || !session.messages || session.messages.length === 0) {
      return '暂无消息，开始对话吧';
    }

    const lastMessage = session.messages[session.messages.length - 1];
    return lastMessage.role === 'user'
      ? `你: ${lastMessage.content}`
      : lastMessage.content;
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60 * 1000) return '刚刚';
    if (diff < 60 * 60 * 1000) return `${Math.floor(diff / (60 * 1000))}分钟前`;
    if (diff < 24 * 60 * 60 * 1000) return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
    
    return date.toLocaleDateString('zh-CN', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className="title">
          <MessageSquare size={16} />
          当前会话
        </div>
        
        <Button
          type="default"
          icon={<Plus size={16} />}
          onClick={handleCreateSession}
          className={styles.newSessionButton}
        >
          新建会话
        </Button>
      </div>
      
      <div className={styles.content}>
        {currentSessions.length > 0 ? (
          <div className={styles.sessionsList}>
            {currentSessions.map((session) => (
              <div
                key={session.id}
                className={`${styles.currentSession} ${session.id === activeSessionId ? styles.activeSession : ''}`}
                onClick={() => handleSessionClick(session)}
              >
                <div className="session-title">
                  <span>{getSessionDisplayTitle(session)}</span>
                  <div className="session-actions">
                    <Dropdown
                      menu={{
                        items: getSessionMenuItems(session),
                        onClick: ({ key }) => handleMenuClick(key, session)
                      }}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button
                        type="text"
                        size="small"
                        icon={<MoreHorizontal size={12} />}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Dropdown>
                  </div>
                </div>
                <div className="session-info">
                  {session.messages?.length || 0} 条消息 · {formatTime(session.updatedAt)}
                </div>
                <div className="session-preview">
                  {getSessionPreview(session)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyState}>
            <div className="empty-icon">💬</div>
            <div className="empty-title">暂无活动会话</div>
            <div className="empty-description">
              点击上方按钮创建新会话，<br />
              或从历史会话中选择一个
            </div>
          </div>
        )}
      </div>

      {/* 定价信息模态框 */}
      {selectedAgent && (
        <AgentPricingModal
          visible={pricingModalVisible}
          onClose={() => {
            setPricingModalVisible(false);
            setSelectedAgent(null);
          }}
          agentId={selectedAgent.id}
          agentName={selectedAgent.name}
        />
      )}
    </div>
  );
});

CurrentSessionPanel.displayName = 'CurrentSessionPanel';

export default CurrentSessionPanel;

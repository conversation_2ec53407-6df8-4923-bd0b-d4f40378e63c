import mysql from 'mysql2/promise';

// 解析 DATABASE_URL 的函数
function parseDatabaseUrl(url: string) {
  try {
    const parsed = new URL(url);
    return {
      host: parsed.hostname,
      port: parseInt(parsed.port) || 3306,
      user: parsed.username,
      password: parsed.password,
      database: parsed.pathname.slice(1), // 移除开头的 '/'
      ssl: parsed.searchParams.get('ssl-mode') === 'REQUIRED' ? { rejectUnauthorized: false } : undefined,
    };
  } catch (error) {
    console.error('解析 DATABASE_URL 失败:', error);
    throw new Error('无效的 DATABASE_URL 格式');
  }
}

// 数据库配置
const getDatabaseConfig = () => {
  // 优先使用 DATABASE_URL
  if (process.env.DATABASE_URL) {
    console.log('使用 DATABASE_URL 配置数据库连接');
    const parsed = parseDatabaseUrl(process.env.DATABASE_URL);
    return {
      ...parsed,
      charset: 'utf8mb4',
      timezone: '+08:00',
    };
  }

  // 回退到单独的环境变量
  console.log('使用单独的环境变量配置数据库连接');
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'story_ai',
    charset: 'utf8mb4',
    timezone: '+08:00',
  };
};

const dbConfig = getDatabaseConfig();

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 200,
  queueLimit: 0,
  idleTimeout: 300000, // 5分钟空闲超时
});

// 获取数据库连接
export async function getConnection() {
  try {
    return await pool.getConnection();
  } catch (error) {
    console.error('数据库连接失败:', error);
    throw new Error('数据库连接失败');
  }
}

// 执行查询
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T> {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('数据库查询失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}

// 执行事务
export async function executeTransaction<T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> {
  const connection = await getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    console.error('事务执行失败:', error);
    throw error;
  } finally {
    connection.release();
  }
}

// 测试数据库连接
export async function testConnection(): Promise<boolean> {
  try {
    const connection = await getConnection();
    await connection.ping();
    connection.release();
    console.log('数据库连接成功');
    return true;
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return false;
  }
}

// 关闭连接池
export async function closePool(): Promise<void> {
  try {
    await pool.end();
    console.log('数据库连接池已关闭');
  } catch (error) {
    console.error('关闭数据库连接池失败:', error);
  }
}

// 获取连接池状态
export function getPoolStatus() {
  try {
    // 检查连接池是否有内部属性可以访问
    const poolInternal = (pool as any).pool;
    if (poolInternal && poolInternal.allConnections) {
      return {
        totalConnections: poolInternal.allConnections.length,
        freeConnections: poolInternal.freeConnections.length,
        acquiringConnections: poolInternal.acquiringConnections.length,
        connectionLimit: poolInternal.config.connectionLimit,
      };
    } else {
      // 如果无法访问内部属性，返回基本信息
      return {
        totalConnections: 0,
        freeConnections: 0,
        acquiringConnections: 0,
        connectionLimit: 200, // 使用配置的默认值
      };
    }
  } catch (error) {
    console.error('获取连接池状态失败:', error);
    return {
      totalConnections: 0,
      freeConnections: 0,
      acquiringConnections: 0,
      connectionLimit: 200,
    };
  }
}

// 监控连接池状态（开发环境）
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    try {
      const status = getPoolStatus();
      if (status.freeConnections < 5 && status.totalConnections > 0) {
        console.warn('⚠️ 数据库连接池警告:', status);
      }
    } catch (error) {
      // 静默处理监控错误，避免影响应用运行
      console.debug('连接池监控错误:', error);
    }
  }, 30000); // 每30秒检查一次
}

export default pool;

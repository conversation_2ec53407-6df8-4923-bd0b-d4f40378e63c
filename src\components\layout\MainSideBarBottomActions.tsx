'use client';

import { ActionIcon, ActionIconProps } from '@lobehub/ui';
import { Book } from 'lucide-react';
import Link from 'next/link';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

const ICON_SIZE: ActionIconProps['size'] = {
  blockSize: 36,
  size: 20,
  strokeWidth: 1.5,
};

const MainSideBarBottomActions = memo(() => {
  return (
    <Flexbox gap={8}>
      <Link aria-label="帮助文档" href="/help">
        <ActionIcon
          icon={Book}
          size={ICON_SIZE}
          title="帮助文档"
          tooltipProps={{ placement: 'right' }}
        />
      </Link>
    </Flexbox>
  );
});

MainSideBarBottomActions.displayName = 'MainSideBarBottomActions';

export default MainSideBarBottomActions;

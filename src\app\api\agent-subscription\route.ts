import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';

// POST - 购买智能体订阅
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      agentId,
      pricingPlanId,
      purchasePrice,
      currency = 'CNY'
    } = body;

    if (!userId || !agentId || !pricingPlanId || !purchasePrice) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID、智能体ID、定价方案ID和购买价格为必填项',
        },
        { status: 400 }
      );
    }

    // 1. 获取定价方案信息
    const pricingPlanQuery = `
      SELECT * FROM agent_pricing_plans 
      WHERE id = ? AND is_active = true
    `;
    
    const pricingPlans = await executeQuery<any[]>(pricingPlanQuery, [pricingPlanId]);
    
    if (pricingPlans.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '定价方案不存在或已停用',
        },
        { status: 404 }
      );
    }

    const pricingPlan = pricingPlans[0];
    const subscriptionId = generateId();
    const now = new Date();

    // 2. 计算订阅参数
    let remainingUsage = 0;
    let totalUsage = 0;
    let startDate = null;
    let endDate = null;

    if (pricingPlan.type === 'per_usage') {
      // 按次收费
      remainingUsage = pricingPlan.usage_count || 0;
      totalUsage = 0;
    } else if (pricingPlan.type === 'time_based') {
      // 按时计费
      startDate = now;
      endDate = new Date(now.getTime() + (pricingPlan.duration_days || 30) * 24 * 60 * 60 * 1000);
    }

    // 3. 创建订阅记录
    const subscriptionQuery = `
      INSERT INTO user_agent_subscriptions (
        id, user_id, agent_id, pricing_plan_id, remaining_usage, total_usage,
        start_date, end_date, status, purchase_price, currency, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const subscriptionParams = [
      subscriptionId,
      userId,
      agentId,
      pricingPlanId,
      remainingUsage,
      totalUsage,
      startDate,
      endDate,
      'active',
      purchasePrice,
      currency,
      now,
      now
    ];

    await executeQuery(subscriptionQuery, subscriptionParams);

    // 4. 如果是定时订阅，为该用户该智能体的未来使用记录设置截止时间
    if (pricingPlan.type === 'time_based' && endDate) {
      // 这里可以选择性地更新现有的使用记录，或者在后续使用时设置截止时间
      // 为了简化，我们在记录使用时动态设置截止时间
    }

    return NextResponse.json({
      success: true,
      message: '订阅购买成功',
      data: {
        subscriptionId,
        type: pricingPlan.type,
        remainingUsage,
        startDate,
        endDate,
        status: 'active'
      },
    });

  } catch (error) {
    console.error('购买订阅失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '购买订阅失败',
      },
      { status: 500 }
    );
  }
}

// GET - 获取用户的智能体订阅
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const agentId = searchParams.get('agentId');

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID为必填项',
        },
        { status: 400 }
      );
    }

    let query = `
      SELECT 
        s.*,
        p.name as plan_name,
        p.type as plan_type,
        p.description as plan_description
      FROM user_agent_subscriptions s
      LEFT JOIN agent_pricing_plans p ON s.pricing_plan_id = p.id
      WHERE s.user_id = ?
    `;
    const params: any[] = [userId];

    if (agentId) {
      query += ' AND s.agent_id = ?';
      params.push(agentId);
    }

    query += ' ORDER BY s.created_at DESC';

    const subscriptions = await executeQuery<any[]>(query, params);

    // 转换字段名为camelCase
    const formattedSubscriptions = subscriptions.map(sub => ({
      id: sub.id,
      userId: sub.user_id,
      agentId: sub.agent_id,
      pricingPlanId: sub.pricing_plan_id,
      remainingUsage: sub.remaining_usage,
      totalUsage: sub.total_usage,
      startDate: sub.start_date,
      endDate: sub.end_date,
      status: sub.status,
      purchasePrice: sub.purchase_price,
      currency: sub.currency,
      createdAt: sub.created_at,
      updatedAt: sub.updated_at,
      planName: sub.plan_name,
      planType: sub.plan_type,
      planDescription: sub.plan_description
    }));

    return NextResponse.json({
      success: true,
      data: formattedSubscriptions,
    });

  } catch (error) {
    console.error('获取订阅失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取订阅失败',
      },
      { status: 500 }
    );
  }
}

// PUT - 更新订阅状态
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { subscriptionId, status } = body;

    if (!subscriptionId || !status) {
      return NextResponse.json(
        {
          success: false,
          error: '订阅ID和状态为必填项',
        },
        { status: 400 }
      );
    }

    const query = `
      UPDATE user_agent_subscriptions 
      SET status = ?, updated_at = ?
      WHERE id = ?
    `;

    await executeQuery(query, [status, new Date(), subscriptionId]);

    return NextResponse.json({
      success: true,
      message: '订阅状态更新成功',
    });

  } catch (error) {
    console.error('更新订阅状态失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '更新订阅状态失败',
      },
      { status: 500 }
    );
  }
}

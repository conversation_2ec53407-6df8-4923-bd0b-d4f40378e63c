-- 创建兑换码表
CREATE TABLE IF NOT EXISTS redemption_codes (
    id VARCHAR(50) PRIMARY KEY,
    agent_id VARCHAR(50) NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    code_type ENUM('duration', 'usage') NOT NULL,
    label VARCHAR(255) NOT NULL,
    -- 时长兑换字段
    duration_days INT NULL,
    -- 次数兑换字段
    usage_count INT NULL,
    -- 安全字段
    hash_code VARCHAR(255) NOT NULL,
    salt VARCHAR(100) NOT NULL,
    -- 状态字段
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP NULL,
    used_by VARCHAR(50) NULL,
    expires_at TIMESTAMP NULL,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_agent_id (agent_id),
    INDEX idx_code (code),
    INDEX idx_code_type (code_type),
    INDEX idx_created_by (created_by),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建兑换码使用记录表
CREATE TABLE IF NOT EXISTS redemption_logs (
    id VARCHAR(50) PRIMARY KEY,
    redemption_code_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    agent_id VARCHAR(50) NOT NULL,
    code_type ENUM('duration', 'usage') NOT NULL,
    -- 兑换获得的内容
    duration_days INT NULL,
    usage_count INT NULL,
    -- 记录字段
    ip_address VARCHAR(45),
    user_agent TEXT,
    redeemed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_redemption_code_id (redemption_code_id),
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_redeemed_at (redeemed_at),
    FOREIGN KEY (redemption_code_id) REFERENCES redemption_codes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建兑换码批次表（用于管理批量生成的兑换码）
CREATE TABLE IF NOT EXISTS redemption_batches (
    id VARCHAR(50) PRIMARY KEY,
    agent_id VARCHAR(50) NOT NULL,
    batch_name VARCHAR(255) NOT NULL,
    code_type ENUM('duration', 'usage') NOT NULL,
    label VARCHAR(255) NOT NULL,
    total_count INT NOT NULL,
    used_count INT DEFAULT 0,
    duration_days INT NULL,
    usage_count INT NULL,
    expires_at TIMESTAMP NULL,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_agent_id (agent_id),
    INDEX idx_created_by (created_by),
    INDEX idx_code_type (code_type),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加兑换码批次关联字段到兑换码表
ALTER TABLE redemption_codes 
ADD COLUMN batch_id VARCHAR(50) NULL,
ADD INDEX idx_batch_id (batch_id),
ADD FOREIGN KEY (batch_id) REFERENCES redemption_batches(id) ON DELETE SET NULL;

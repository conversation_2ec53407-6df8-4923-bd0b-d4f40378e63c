'use client';

import { memo, useEffect, useRef } from 'react';
import { Flexbox } from 'react-layout-kit';
import { createStyles } from 'antd-style';
import { useChatStore } from '@/store/chat';
import ChatMessage from '@/components/chat/ChatMessage';
import WelcomeMessage from '@/components/chat/WelcomeMessage';

const useStyles = createStyles(({ css, token }) => ({
  messageList: css`
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    padding: 16px;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: ${token.colorBorderSecondary};
      border-radius: 3px;

      &:hover {
        background: ${token.colorBorder};
      }
    }
  `,
}));

interface ChatListProps {
  mobile?: boolean;
}

const ChatList = memo<ChatListProps>(({ mobile }) => {
  const { styles } = useStyles();
  const { currentSessions, activeSessionId } = useChatStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 获取当前活跃的会话
  const currentSession = currentSessions.find(session => session.id === activeSessionId);
  const messages = currentSession?.messages || [];

  // 自动滚动到最新消息
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <Flexbox
      flex={1}
      style={{
        overflowX: 'hidden',
        overflowY: 'auto',
        position: 'relative',
      }}
      width={'100%'}
    >
      <div className={styles.messageList}>
        {messages.length === 0 ? (
          <WelcomeMessage />
        ) : (
          messages.map((message, index) => (
            <ChatMessage
              key={message.id || index}
              message={message}
              index={index}
            />
          ))
        )}
        {/* 用于自动滚动到底部的锚点 */}
        <div ref={messagesEndRef} />
      </div>
    </Flexbox>
  );
});

export default ChatList;

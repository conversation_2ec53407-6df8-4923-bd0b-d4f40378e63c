'use client';

import React from 'react';
import { createStyles } from 'antd-style';
import { Avatar, Button, Tooltip, Typography } from 'antd';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ThumbsUp, ThumbsDown, RotateCcw } from 'lucide-react';
import { Message, FileInfo } from '@/types';

const { Text, Paragraph } = Typography;

const useStyles = createStyles(({ token, css }) => ({
  messageItem: css`
    display: flex;
    gap: ${token.marginMD}px;
    margin-bottom: ${token.marginLG}px;
    
    &.user {
      flex-direction: row-reverse;
      
      .message-content {
        background: ${token.colorPrimary};
        color: ${token.colorWhite};
        margin-left: ${token.marginXL * 2}px;
        margin-right: 0;
      }
    }
    
    &.assistant {
      .message-content {
        background: ${token.colorBgContainer};
        border: 1px solid ${token.colorBorder};
        margin-right: ${token.marginXL * 2}px;
        margin-left: 0;
      }
    }
  `,
  
  avatar: css`
    flex-shrink: 0;
    
    .ant-avatar {
      background: ${token.colorBgContainer};
      border: 1px solid ${token.colorBorder};
      color: ${token.colorText};
      
      &.user-avatar {
        background: ${token.colorPrimary};
        color: ${token.colorWhite};
        border-color: ${token.colorPrimary};
      }
      
      &.assistant-avatar {
        background: linear-gradient(135deg, ${token.colorPrimary}, ${token.colorPrimaryActive});
        color: ${token.colorWhite};
        border: none;
      }
    }
  `,
  
  messageContent: css`
    flex: 1;
    border-radius: ${token.borderRadiusLG}px;
    padding: ${token.paddingMD}px ${token.paddingLG}px;
    position: relative;
    word-break: break-word;
    
    .message-text {
      margin: 0;
      line-height: 1.6;
      
      pre {
        background: rgba(0, 0, 0, 0.05);
        padding: ${token.paddingSM}px;
        border-radius: ${token.borderRadius}px;
        overflow-x: auto;
        margin: ${token.marginSM}px 0;
      }
      
      code {
        background: rgba(0, 0, 0, 0.05);
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      }
    }
    
    .message-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: ${token.marginSM}px;
      font-size: ${token.fontSizeSM}px;
      opacity: 0.7;
    }
  `,
  
  actions: css`
    display: flex;
    gap: ${token.marginXS}px;
    opacity: 0;
    transition: opacity 0.2s;
    
    .message-item:hover & {
      opacity: 1;
    }
    
    .ant-btn {
      border: none;
      box-shadow: none;
      padding: 4px;
      height: auto;
      min-width: auto;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  `,
  
  timestamp: css`
    font-size: ${token.fontSizeSM}px;
    color: ${token.colorTextTertiary};
  `,
  
  status: css`
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
    font-size: ${token.fontSizeSM}px;
    
    &.sending {
      color: ${token.colorWarning};
    }
    
    &.error {
      color: ${token.colorError};
    }
    
    &.success {
      color: ${token.colorSuccess};
    }
  `,
  
  fileAttachment: css`
    background: rgba(0, 0, 0, 0.05);
    border-radius: ${token.borderRadius}px;
    padding: ${token.paddingSM}px;
    margin: ${token.marginSM}px 0;
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;
    
    .file-icon {
      width: 24px;
      height: 24px;
      background: ${token.colorPrimary};
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: ${token.colorWhite};
      font-size: 12px;
    }
    
    .file-info {
      flex: 1;
      
      .file-name {
        font-weight: 500;
        margin: 0;
      }
      
      .file-size {
        font-size: ${token.fontSizeSM}px;
        color: ${token.colorTextTertiary};
        margin: 0;
      }
    }
  `,
}));

interface MessageItemProps {
  message: Message;
  onCopy?: (text: string) => void;
  onRegenerate?: (messageId: string) => void;
  onFeedback?: (messageId: string, type: 'like' | 'dislike') => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onCopy,
  onRegenerate,
  onFeedback,
}) => {
  const { styles } = useStyles();

  const handleCopy = () => {
    navigator.clipboard.writeText(message.content);
    onCopy?.(message.content);
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderFileAttachment = (file: FileInfo) => (
    <div key={file.name} className={styles.fileAttachment}>
      <div className="file-icon">
        {file.type?.startsWith('image/') ? '🖼️' : '📄'}
      </div>
      <div className="file-info">
        <div className="file-name">{file.name}</div>
        <div className="file-size">{(file.size / 1024).toFixed(1)} KB</div>
      </div>
    </div>
  );

  return (
    <div className={`${styles.messageItem} ${message.role} message-item`}>
      <div className={styles.avatar}>
        <Avatar
          size={36}
          icon={message.role === 'user' ? <User size={18} /> : <Bot size={18} />}
          className={`${message.role}-avatar`}
        />
      </div>
      
      <div className={`${styles.messageContent} message-content`}>
        {/* 文件附件 */}
        {message.files && message.files.length > 0 && (
          <div>
            {message.files.map(renderFileAttachment)}
          </div>
        )}
        
        {/* 消息内容 */}
        <Paragraph className="message-text">
          {message.content}
        </Paragraph>
        
        {/* 消息元信息 */}
        <div className="message-meta">
          <div className={styles.timestamp}>
            {formatTimestamp(message.timestamp)}
          </div>
          
          <div className={styles.actions}>
            <Tooltip title="复制">
              <Button
                type="text"
                size="small"
                icon={<Copy size={14} />}
                onClick={handleCopy}
              />
            </Tooltip>
            
            {message.role === 'assistant' && (
              <>
                <Tooltip title="重新生成">
                  <Button
                    type="text"
                    size="small"
                    icon={<RotateCcw size={14} />}
                    onClick={() => onRegenerate?.(message.id)}
                  />
                </Tooltip>
                
                <Tooltip title="有用">
                  <Button
                    type="text"
                    size="small"
                    icon={<ThumbsUp size={14} />}
                    onClick={() => onFeedback?.(message.id, 'like')}
                  />
                </Tooltip>
                
                <Tooltip title="无用">
                  <Button
                    type="text"
                    size="small"
                    icon={<ThumbsDown size={14} />}
                    onClick={() => onFeedback?.(message.id, 'dislike')}
                  />
                </Tooltip>
              </>
            )}
          </div>
        </div>
        
        {/* 消息状态 */}
        {message.status && message.status !== 'sent' && (
          <div className={`${styles.status} ${message.status}`}>
            {message.status === 'sending' && '发送中...'}
            {message.status === 'error' && '发送失败'}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;
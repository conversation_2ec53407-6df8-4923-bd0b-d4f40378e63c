import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { generateId } from '@/lib/utils';

// 计算剩余次数的辅助函数
async function calculateRemainingCount(userId: string, agentId: string, currentUsageCount: number = 0): Promise<number | null> {
  try {
    // 1. 获取智能体的试用次数
    const agentQuery = `SELECT trial_usage_count FROM agents WHERE id = ? LIMIT 1`;
    const agentResult = await executeQuery<any[]>(agentQuery, [agentId]);
    const trialCount = agentResult.length > 0 ? (agentResult[0].trial_usage_count || 3) : 3;

    // 2. 检查用户是否有有效的付费订阅
    const subscriptionQuery = `
      SELECT COUNT(*) as count
      FROM user_agent_subscriptions
      WHERE user_id = ?
        AND agent_id = ?
        AND status = 'active'
        AND (
          (remaining_usage > 0) OR
          (end_date IS NOT NULL AND end_date > NOW())
        )
      LIMIT 1
    `;
    const subscriptionResult = await executeQuery<any[]>(subscriptionQuery, [userId, agentId]);
    const hasSubscription = subscriptionResult.length > 0 && subscriptionResult[0].count > 0;

    // 如果有付费订阅，返回 null 表示无限制
    if (hasSubscription) {
      return null;
    }

    // 3. 获取已使用次数（现在一个用户+智能体只有一条记录）
    const usageQuery = `
      SELECT COALESCE(usage_count, 0) as total_used
      FROM agent_usage_logs
      WHERE user_id = ? AND agent_id = ?
      LIMIT 1
    `;
    const usageResult = await executeQuery<any[]>(usageQuery, [userId, agentId]);
    const totalUsed = usageResult.length > 0 ? usageResult[0].total_used : 0;

    // 4. 计算剩余次数（包含当前这次使用）
    const remaining = trialCount - (totalUsed + currentUsageCount);
    return Math.max(0, remaining);
  } catch (error) {
    console.error('计算剩余次数失败:', error);
    return 0; // 出错时返回0，保守处理
  }
}

// POST - 记录智能体使用次数
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      agentId,
      sessionId,
      usageType = 'chat',
      tokensUsed = 0,
      cost = 0,
      usageCount = 1,
      expiryDate = null
    } = body;

    if (!userId || !agentId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID和智能体ID为必填项',
        },
        { status: 400 }
      );
    }

    // 1. 检查是否已存在该用户+智能体的记录
    const existingQuery = `
      SELECT id, usage_count, tokens_used, cost, remaining_count, created_at
      FROM agent_usage_logs
      WHERE user_id = ? AND agent_id = ?
      LIMIT 1
    `;

    const existingRecords = await executeQuery<any[]>(existingQuery, [userId, agentId]);

    let logId: string;

    if (existingRecords.length > 0) {
      // 记录已存在，更新累加
      console.log('找到现有记录，执行更新累加');
      const existingRecord = existingRecords[0];
      logId = existingRecord.id;

      // 计算更新后的剩余次数（基于现有记录的使用次数）
      const newUsageCount = existingRecord.usage_count + usageCount;

      // 获取智能体的试用次数
      const agentQuery = `SELECT trial_usage_count FROM agents WHERE id = ? LIMIT 1`;
      const agentResult = await executeQuery<any[]>(agentQuery, [agentId]);
      const trialCount = agentResult.length > 0 ? (agentResult[0].trial_usage_count || 3) : 3;

      const remainingCount = Math.max(0, trialCount - newUsageCount);

      const updateQuery = `
        UPDATE agent_usage_logs
        SET usage_count = usage_count + ?,
            tokens_used = tokens_used + ?,
            cost = cost + ?,
            session_id = ?,
            expiry_date = COALESCE(?, expiry_date),
            remaining_count = ?
        WHERE id = ?
      `;

      const updateParams = [
        usageCount,
        tokensUsed,
        cost,
        sessionId || null,
        expiryDate ? new Date(expiryDate) : null,
        remainingCount,
        logId
      ];

      const updateResult = await executeQuery(updateQuery, updateParams);
    } else {
      // 记录不存在，插入新记录
      logId = generateId();

      // 计算剩余次数
      const remainingCount = await calculateRemainingCount(userId, agentId, usageCount);

      const insertQuery = `
        INSERT INTO agent_usage_logs (
          id, user_id, agent_id, session_id, usage_type, tokens_used, cost, usage_count, expiry_date, remaining_count, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const insertParams = [
        logId,
        userId,
        agentId,
        sessionId || null,
        usageType,
        tokensUsed,
        cost,
        usageCount,
        expiryDate ? new Date(expiryDate) : null,
        remainingCount,
        new Date(),
      ];

      console.log('插入参数:', insertParams);
      const insertResult = await executeQuery(insertQuery, insertParams);
      console.log('插入结果:', insertResult);
      console.log('插入新记录完成，logId:', logId);
    }

    // 2. 更新智能体的总使用次数（按实际使用次数增加）
    const updateAgentQuery = `
      UPDATE agents
      SET usage_count = usage_count + ?, updated_at = ?
      WHERE id = ?
    `;

    await executeQuery(updateAgentQuery, [usageCount, new Date(), agentId]);

    // 3. 查找用户的智能体订阅（如果存在）
    const subscriptionQuery = `
      SELECT id, remaining_usage, total_usage, end_date
      FROM user_agent_subscriptions
      WHERE user_id = ? AND agent_id = ? AND status = 'active'
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const subscriptions = await executeQuery<any[]>(subscriptionQuery, [userId, agentId]);

    // 4. 如果有订阅，更新订阅的使用次数
    if (subscriptions.length > 0) {
      const subscription = subscriptions[0];

      // 判断是否为时间订阅（有end_date且未过期）
      const isTimeBasedSubscription = subscription.end_date && new Date(subscription.end_date) > new Date();

      let updateSubscriptionQuery;
      let updateParams;

      if (isTimeBasedSubscription) {
        // 时间订阅：只更新总使用次数，不扣减剩余次数
        updateSubscriptionQuery = `
          UPDATE user_agent_subscriptions
          SET total_usage = total_usage + ?,
              updated_at = ?
          WHERE id = ?
        `;
        updateParams = [usageCount, new Date(), subscription.id];
      } else {
        // 次数订阅：更新总使用次数并扣减剩余次数
        updateSubscriptionQuery = `
          UPDATE user_agent_subscriptions
          SET total_usage = total_usage + ?,
              remaining_usage = GREATEST(0, remaining_usage - ?),
              updated_at = ?
          WHERE id = ?
        `;
        updateParams = [usageCount, usageCount, new Date(), subscription.id];
      }

      await executeQuery(updateSubscriptionQuery, updateParams);

      // 更新使用日志中的订阅ID
      const updateLogQuery = `
        UPDATE agent_usage_logs
        SET subscription_id = ?
        WHERE id = ?
      `;

      await executeQuery(updateLogQuery, [subscription.id, logId]);
    }

    return NextResponse.json({
      success: true,
      message: '使用次数记录成功',
      data: {
        logId,
        agentId,
        userId,
        usageType,
      },
    });

  } catch (error) {
    console.error('记录智能体使用次数失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '记录使用次数失败',
      },
      { status: 500 }
    );
  }
}

// GET - 获取智能体使用统计
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const agentId = searchParams.get('agentId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID为必填项',
        },
        { status: 400 }
      );
    }

    let query = `
      SELECT
        agent_id,
        usage_count as total_usage_count,
        tokens_used as total_tokens,
        cost as total_cost,
        remaining_count,
        DATE(created_at) as usage_date,
        expiry_date as latest_expiry_date,
        created_at
      FROM agent_usage_logs
      WHERE user_id = ?
    `;
    const params: any[] = [userId];

    // 智能体筛选
    if (agentId) {
      query += ' AND agent_id = ?';
      params.push(agentId);
    }

    // 日期范围筛选
    if (startDate) {
      query += ' AND created_at >= ?';
      params.push(new Date(startDate));
    }

    if (endDate) {
      query += ' AND created_at <= ?';
      params.push(new Date(endDate));
    }

    query += ' ORDER BY created_at DESC';

    const stats = await executeQuery<any[]>(query, params);

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error('获取使用统计失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '获取使用统计失败',
      },
      { status: 500 }
    );
  }
}

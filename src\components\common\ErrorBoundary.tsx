'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: ${token.paddingLG}px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  `,
  
  errorDetails: css`
    margin-top: ${token.marginLG}px;
    padding: ${token.paddingMD}px;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorder};
    border-radius: ${token.borderRadius}px;
    font-family: monospace;
    font-size: ${token.fontSizeSM}px;
    color: ${token.colorTextSecondary};
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
  `,
}));

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 在开发环境下打印错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 UI
      return (
        <ErrorBoundaryContent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

interface ErrorBoundaryContentProps {
  error?: Error;
  errorInfo?: ErrorInfo;
  onRetry: () => void;
}

const ErrorBoundaryContent: React.FC<ErrorBoundaryContentProps> = ({
  error,
  errorInfo,
  onRetry,
}) => {
  const { styles } = useStyles();
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className={styles.container}>
      <Result
        status="error"
        title="页面出现错误"
        subTitle="抱歉，页面遇到了一些问题。您可以尝试刷新页面或联系技术支持。"
        extra={[
          <Button type="primary" key="retry" onClick={onRetry}>
            重试
          </Button>,
          <Button key="reload" onClick={() => window.location.reload()}>
            刷新页面
          </Button>,
        ]}
      >
        {isDevelopment && error && (
          <div className={styles.errorDetails}>
            <strong>错误信息:</strong>
            {'\n'}
            {error.message}
            {'\n\n'}
            <strong>错误堆栈:</strong>
            {'\n'}
            {error.stack}
            {errorInfo && (
              <>
                {'\n\n'}
                <strong>组件堆栈:</strong>
                {'\n'}
                {errorInfo.componentStack}
              </>
            )}
          </div>
        )}
      </Result>
    </div>
  );
};

export default ErrorBoundary;

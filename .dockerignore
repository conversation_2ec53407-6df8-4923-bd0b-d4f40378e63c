# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
.next
out
dist
build

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 微任务
.microbundle-cache

# 可选的 REPL 历史
.node_repl_history

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# 测试
coverage
.nyc_output

# 其他
*.tgz
*.tar.gz
.cache
.parcel-cache

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# 脚本
scripts/
*.sh

# 文档
docs/
*.md

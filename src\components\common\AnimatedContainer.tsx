'use client';

import React from 'react';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  fadeIn: css`
    animation: fadeIn 0.3s ease-in-out;
    
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `,
  
  slideIn: css`
    animation: slideIn 0.4s ease-out;
    
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }
  `,
  
  scaleIn: css`
    animation: scaleIn 0.3s ease-out;
    
    @keyframes scaleIn {
      from {
        opacity: 0;
        transform: scale(0.9);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
  `,
  
  bounceIn: css`
    animation: bounceIn 0.6s ease-out;
    
    @keyframes bounceIn {
      0% {
        opacity: 0;
        transform: scale(0.3);
      }
      50% {
        opacity: 1;
        transform: scale(1.05);
      }
      70% {
        transform: scale(0.9);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
  `,
  
  hover: css`
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  `,
  
  pulse: css`
    animation: pulse 2s infinite;
    
    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.02);
      }
      100% {
        transform: scale(1);
      }
    }
  `,
  
  delayed: css`
    animation-delay: 0.1s;
    animation-fill-mode: both;
  `,
  
  delayed2: css`
    animation-delay: 0.2s;
    animation-fill-mode: both;
  `,
  
  delayed3: css`
    animation-delay: 0.3s;
    animation-fill-mode: both;
  `,
}));

export type AnimationType = 'fadeIn' | 'slideIn' | 'scaleIn' | 'bounceIn' | 'pulse';

interface AnimatedContainerProps {
  children: React.ReactNode;
  animation?: AnimationType;
  hover?: boolean;
  delay?: 0 | 1 | 2 | 3;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const AnimatedContainer: React.FC<AnimatedContainerProps> = ({
  children,
  animation = 'fadeIn',
  hover = false,
  delay = 0,
  className = '',
  style,
  onClick,
}) => {
  const { styles, cx } = useStyles();

  const getDelayClass = () => {
    switch (delay) {
      case 1:
        return styles.delayed;
      case 2:
        return styles.delayed2;
      case 3:
        return styles.delayed3;
      default:
        return '';
    }
  };

  const containerClass = cx(
    styles[animation],
    hover && styles.hover,
    getDelayClass(),
    className
  );

  return (
    <div 
      className={containerClass} 
      style={style}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default AnimatedContainer;

#!/usr/bin/env node

/**
 * 数据库同步脚本 - 将本地数据库同步到远程Aiven数据库
 * 使用方法: node scripts/sync-to-aiven.js
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 本地数据库配置
const LOCAL_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'story_ai',
  charset: 'utf8mb4'
};

// 远程Aiven数据库配置
const REMOTE_CONFIG = {
  host: 'mysql-6eeec90-vankay966-ba73.b.aivencloud.com',
  port: 24287,
  user: 'avnadmin',
  password: 'AVNS_M5iZ4MKAD4iM98BD6PU',
  database: 'defaultdb',
  charset: 'utf8mb4',
  ssl: {
    rejectUnauthorized: false // Aiven通常需要SSL连接
  }
};

// 需要同步的表（按依赖关系排序）
const TABLES_TO_SYNC = [
  'users',                        // 基础表
  'agents',                       // 基础表
  'ai_provider_configs',          // 配置表
  'prompts',                      // 提示词表
  'sessions',                     // 会话表
  'agent_pricing_plans',          // 智能体定价计划（如果存在）
  'user_agent_subscriptions',     // 用户订阅（依赖users, agents, agent_pricing_plans）
  'agent_usage_logs',             // 使用日志（依赖user_agent_subscriptions）
  'redemption_batches',           // 兑换码批次（依赖agents, users）
  'redemption_codes',             // 兑换码（依赖agents, users, redemption_batches）
  'user_redemptions',             // 用户兑换记录（依赖users, redemption_codes）
  'redemption_logs',              // 兑换日志（依赖users, redemption_codes, agents）
  'user_settings',                // 用户设置
  'chat_sessions',                // 聊天会话
  'chat_messages'                 // 聊天消息
];

class DatabaseSyncer {
  constructor() {
    this.localConnection = null;
    this.remoteConnection = null;
  }

  async connect() {
    console.log('🔌 连接到本地数据库...');
    this.localConnection = await mysql.createConnection(LOCAL_CONFIG);
    
    console.log('🌐 连接到远程Aiven数据库...');
    this.remoteConnection = await mysql.createConnection(REMOTE_CONFIG);
    
    console.log('✅ 数据库连接成功');
  }

  async disconnect() {
    if (this.localConnection) {
      await this.localConnection.end();
    }
    if (this.remoteConnection) {
      await this.remoteConnection.end();
    }
    console.log('🔌 数据库连接已关闭');
  }

  async disableForeignKeyChecks() {
    console.log('🔓 禁用外键检查...');
    await this.remoteConnection.execute('SET FOREIGN_KEY_CHECKS = 0');
  }

  async enableForeignKeyChecks() {
    console.log('🔒 启用外键检查...');
    await this.remoteConnection.execute('SET FOREIGN_KEY_CHECKS = 1');
  }

  async getTableStructure(connection, tableName) {
    const [rows] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
    return rows[0]['Create Table'];
  }

  async tableExists(connection, tableName) {
    try {
      const [rows] = await connection.execute(
        `SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?`,
        [connection.config.database, tableName]
      );
      return rows.length > 0;
    } catch (error) {
      return false;
    }
  }

  async createTable(tableName) {
    console.log(`📋 创建表: ${tableName}`);

    try {
      const createTableSQL = await this.getTableStructure(this.localConnection, tableName);
      await this.remoteConnection.execute(createTableSQL);
      console.log(`✅ 表 ${tableName} 创建成功`);
    } catch (error) {
      console.error(`❌ 创建表 ${tableName} 失败:`, error.message);
      throw error;
    }
  }

  async getTableColumns(connection, tableName) {
    const [rows] = await connection.execute(
      `SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
       FROM information_schema.columns
       WHERE table_schema = ? AND table_name = ?
       ORDER BY ordinal_position`,
      [connection.config.database, tableName]
    );
    return rows;
  }

  async syncTableStructure(tableName) {
    console.log(`🔧 同步表结构: ${tableName}`);

    try {
      // 获取本地和远程表的列信息
      const localColumns = await this.getTableColumns(this.localConnection, tableName);
      const remoteColumns = await this.getTableColumns(this.remoteConnection, tableName);

      // 创建列名映射
      const localColumnMap = new Map(localColumns.map(col => [col.COLUMN_NAME, col]));
      const remoteColumnMap = new Map(remoteColumns.map(col => [col.COLUMN_NAME, col]));

      // 检查需要添加的列
      const columnsToAdd = [];
      for (const [columnName, columnInfo] of localColumnMap) {
        if (!remoteColumnMap.has(columnName)) {
          columnsToAdd.push(columnInfo);
        }
      }

      // 添加缺失的列
      for (const column of columnsToAdd) {
        console.log(`➕ 添加列: ${tableName}.${column.COLUMN_NAME}`);

        let alterSQL = `ALTER TABLE \`${tableName}\` ADD COLUMN \`${column.COLUMN_NAME}\` ${column.COLUMN_TYPE}`;

        if (column.IS_NULLABLE === 'NO') {
          alterSQL += ' NOT NULL';
        }

        if (column.COLUMN_DEFAULT !== null) {
          if (column.COLUMN_DEFAULT === 'CURRENT_TIMESTAMP') {
            alterSQL += ' DEFAULT CURRENT_TIMESTAMP';
          } else {
            alterSQL += ` DEFAULT '${column.COLUMN_DEFAULT}'`;
          }
        }

        if (column.EXTRA) {
          alterSQL += ` ${column.EXTRA}`;
        }

        try {
          await this.remoteConnection.execute(alterSQL);
          console.log(`✅ 列 ${column.COLUMN_NAME} 添加成功`);
        } catch (error) {
          console.error(`❌ 添加列 ${column.COLUMN_NAME} 失败:`, error.message);
          // 继续处理其他列，不中断整个过程
        }
      }

      if (columnsToAdd.length === 0) {
        console.log(`✅ 表 ${tableName} 结构已是最新`);
      } else {
        console.log(`✅ 表 ${tableName} 结构同步完成，添加了 ${columnsToAdd.length} 个列`);
      }

    } catch (error) {
      console.error(`❌ 同步表结构 ${tableName} 失败:`, error.message);
      throw error;
    }
  }

  async syncTableData(tableName) {
    console.log(`📊 同步表数据: ${tableName}`);
    
    try {
      // 获取本地表数据
      const [localRows] = await this.localConnection.execute(`SELECT * FROM \`${tableName}\``);
      
      if (localRows.length === 0) {
        console.log(`ℹ️  表 ${tableName} 无数据，跳过同步`);
        return;
      }

      // 清空远程表数据
      await this.remoteConnection.execute(`DELETE FROM \`${tableName}\``);
      
      // 获取表结构信息
      const [columns] = await this.localConnection.execute(
        `SELECT COLUMN_NAME FROM information_schema.columns WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position`,
        [this.localConnection.config.database, tableName]
      );
      
      const columnNames = columns.map(col => col.COLUMN_NAME);
      const placeholders = columnNames.map(() => '?').join(', ');
      const columnList = columnNames.map(name => `\`${name}\``).join(', ');
      
      const insertSQL = `INSERT INTO \`${tableName}\` (${columnList}) VALUES (${placeholders})`;
      
      // 批量插入数据
      const batchSize = 100;
      for (let i = 0; i < localRows.length; i += batchSize) {
        const batch = localRows.slice(i, i + batchSize);
        
        for (const row of batch) {
          const values = columnNames.map(col => row[col]);
          await this.remoteConnection.execute(insertSQL, values);
        }
        
        console.log(`📈 已同步 ${Math.min(i + batchSize, localRows.length)}/${localRows.length} 条记录`);
      }
      
      console.log(`✅ 表 ${tableName} 数据同步完成 (${localRows.length} 条记录)`);
    } catch (error) {
      console.error(`❌ 同步表 ${tableName} 数据失败:`, error.message);
      throw error;
    }
  }

  async syncDatabase() {
    console.log('🚀 开始数据库同步...');
    console.log(`📍 本地数据库: ${LOCAL_CONFIG.host}:${LOCAL_CONFIG.port}/${LOCAL_CONFIG.database}`);
    console.log(`📍 远程数据库: ${REMOTE_CONFIG.host}:${REMOTE_CONFIG.port}/${REMOTE_CONFIG.database}`);
    console.log('');

    try {
      await this.connect();

      // 禁用外键检查
      await this.disableForeignKeyChecks();

      for (const tableName of TABLES_TO_SYNC) {
        console.log(`\n🔄 处理表: ${tableName}`);

        // 检查本地表是否存在
        const localExists = await this.tableExists(this.localConnection, tableName);
        if (!localExists) {
          console.log(`⚠️  本地表 ${tableName} 不存在，跳过`);
          continue;
        }

        // 检查远程表是否存在，不存在则创建
        const remoteExists = await this.tableExists(this.remoteConnection, tableName);
        if (!remoteExists) {
          await this.createTable(tableName);
        } else {
          // 表存在，同步表结构（添加缺失的列）
          await this.syncTableStructure(tableName);
        }

        // 同步数据
        await this.syncTableData(tableName);
      }

      // 重新启用外键检查
      await this.enableForeignKeyChecks();

      console.log('\n🎉 数据库同步完成！');

    } catch (error) {
      console.error('\n❌ 同步过程中发生错误:', error.message);

      // 确保重新启用外键检查
      try {
        await this.enableForeignKeyChecks();
      } catch (fkError) {
        console.error('❌ 重新启用外键检查失败:', fkError.message);
      }

      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }

  async createBackup() {
    console.log('💾 创建远程数据库备份...');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, '..', 'backups');

    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    for (const tableName of TABLES_TO_SYNC) {
      try {
        const remoteExists = await this.tableExists(this.remoteConnection, tableName);
        if (!remoteExists) continue;

        const [rows] = await this.remoteConnection.execute(`SELECT * FROM \`${tableName}\``);
        if (rows.length === 0) continue;

        const backupFile = path.join(backupDir, `${tableName}_backup_${timestamp}.json`);
        fs.writeFileSync(backupFile, JSON.stringify(rows, null, 2));
        console.log(`📁 备份表 ${tableName}: ${rows.length} 条记录`);
      } catch (error) {
        console.warn(`⚠️  备份表 ${tableName} 失败:`, error.message);
      }
    }

    console.log(`✅ 备份完成，保存在: ${backupDir}`);
  }
}

// 交互式确认函数
function askQuestion(question) {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer.toLowerCase().trim());
    });
  });
}

// 主函数
async function main() {
  console.log('='.repeat(60));
  console.log('📦 数据库同步工具 - 本地到Aiven（外键约束处理）');
  console.log('='.repeat(60));
  console.log('⚠️  警告: 此操作将覆盖远程数据库的所有数据！');
  console.log('📍 远程数据库: mysql-6eeec90-vankay966-ba73.b.aivencloud.com:24287/defaultdb');
  console.log('🔧 将自动创建缺失的表和字段');
  console.log('🔓 将临时禁用外键检查以避免约束冲突');
  console.log('');

  // 确认操作
  const confirm = await askQuestion('确定要继续同步吗？(yes/no): ');
  if (confirm !== 'yes' && confirm !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  const backup = await askQuestion('是否先创建远程数据库备份？(yes/no): ');

  const syncer = new DatabaseSyncer();

  try {
    await syncer.connect();

    if (backup === 'yes' || backup === 'y') {
      await syncer.createBackup();
      console.log('');
    }

    await syncer.syncDatabase();
  } finally {
    await syncer.disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DatabaseSyncer;

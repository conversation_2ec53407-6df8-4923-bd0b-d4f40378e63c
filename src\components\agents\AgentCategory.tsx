'use client';

import React from 'react';
import { <PERSON>u, Badge } from 'antd';
import {
  Megaphone,
  BookOpen,
  Video,
  MoreHorizontal,
  Grid3X3
} from 'lucide-react';
import { createStyles } from 'antd-style';
import { useAgentStore } from '@/store/agent';
import { AgentCategory as AgentCategoryType, AGENT_CATEGORIES } from '@/types/agent';

const useStyles = createStyles(({ css, token }) => ({
  categoryMenu: css`
    border: none;
    background: transparent;
    
    .ant-menu-item {
      border-radius: 8px;
      margin-bottom: 4px;
      padding: 8px 12px;
      height: auto;
      line-height: 1.4;
      
      &:hover {
        background: ${token.colorFillTertiary};
      }
      
      &.ant-menu-item-selected {
        background: ${token.colorPrimaryBg};
        color: ${token.colorPrimary};
        
        .ant-menu-item-icon {
          color: ${token.colorPrimary};
        }
      }
    }
    
    .ant-menu-item-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  `,
  menuItemContent: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  `,
  menuItemLabel: css`
    display: flex;
    align-items: center;
    gap: 8px;
  `,
  menuItemCount: css`
    font-size: 12px;
    color: ${token.colorTextTertiary};
  `,
}));

// 图标映射
const iconMap = {
  Megaphone,
  BookOpen,
  Video,
  MoreHorizontal,
  Grid3X3,
};

const AgentCategory: React.FC = () => {
  const { styles } = useStyles();
  const { agents, selectedCategory, setSelectedCategory } = useAgentStore();

  // 计算每个分类的智能体数量
  const getCategoryCount = (category: AgentCategoryType | 'all') => {
    if (category === 'all') {
      return agents.length;
    }
    return agents.filter(agent => agent.category === category).length;
  };

  // 构建菜单项
  const menuItems = [
    {
      key: 'all',
      icon: <Grid3X3 size={16} />,
      label: (
        <div className={styles.menuItemContent}>
          <div className={styles.menuItemLabel}>
            <span>全部智能体</span>
          </div>
          <Badge 
            count={getCategoryCount('all')} 
            size="small" 
            style={{ backgroundColor: '#f0f0f0', color: '#666' }}
          />
        </div>
      ),
    },
    ...AGENT_CATEGORIES.map(category => {
      const IconComponent = iconMap[category.icon as keyof typeof iconMap] || MoreHorizontal;
      const count = getCategoryCount(category.key);
      
      return {
        key: category.key,
        icon: <IconComponent size={16} />,
        label: (
          <div className={styles.menuItemContent}>
            <div className={styles.menuItemLabel}>
              <span>{category.label}</span>
            </div>
            {count > 0 && (
              <Badge 
                count={count} 
                size="small" 
                style={{ backgroundColor: '#f0f0f0', color: '#666' }}
              />
            )}
          </div>
        ),
      };
    })
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    setSelectedCategory(key as AgentCategoryType | 'all');
  };

  return (
    <Menu
      className={styles.categoryMenu}
      mode="inline"
      selectedKeys={[selectedCategory]}
      items={menuItems}
      onClick={handleMenuClick}
    />
  );
};

export default AgentCategory;

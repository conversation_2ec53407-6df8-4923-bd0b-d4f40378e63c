-- 为智能体表添加头像和预设提示词字段
-- 执行时间: 2024-01-XX

-- 添加头像字段（如果不存在）
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS avatar VARCHAR(500) COMMENT '智能体头像URL';

-- 添加预设提示词字段（如果不存在）
ALTER TABLE agents 
ADD COLUMN IF NOT EXISTS preset_prompts JSON COMMENT '预设提示词列表，存储为JSON数组';

-- 为新字段添加索引（可选，提高查询性能）
-- CREATE INDEX IF NOT EXISTS idx_agents_avatar ON agents(avatar);

-- 更新现有记录的默认值（可选）
-- UPDATE agents SET preset_prompts = '[]' WHERE preset_prompts IS NULL;

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'agents' 
-- AND COLUMN_NAME IN ('avatar', 'preset_prompts');

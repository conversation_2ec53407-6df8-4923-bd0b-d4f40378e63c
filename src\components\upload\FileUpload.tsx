'use client';

import React, { useState, useCallback } from 'react';
import { createStyles } from 'antd-style';
import {
  Upload,
  Button,
  Card,
  List,
  Progress,
  message,
  Space,
  Tag,
  Tooltip,
  Modal,
  Image,
} from 'antd';
import {
  Upload as UploadIcon,
  File,
  Image as ImageIcon,
  FileText,
  Trash2,
  Eye,
  Download,
} from 'lucide-react';
import { FileInfo, FileUploadOptions, fileUploadService } from '@/services/fileUpload';

const { Dragger } = Upload;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    .ant-upload-drag {
      border: 2px dashed ${token.colorBorder};
      border-radius: ${token.borderRadius}px;
      background: ${token.colorFillAlter};
      transition: all 0.3s ease;
      
      &:hover {
        border-color: ${token.colorPrimary};
        background: ${token.colorPrimaryBg};
      }
      
      &.ant-upload-drag-hover {
        border-color: ${token.colorPrimary};
        background: ${token.colorPrimaryBg};
      }
    }
    
    .ant-upload-drag-icon {
      color: ${token.colorTextTertiary};
      font-size: 48px;
      margin-bottom: ${token.marginSM}px;
    }
    
    .ant-upload-text {
      color: ${token.colorText};
      font-size: ${token.fontSizeLG}px;
      margin-bottom: ${token.marginXS}px;
    }
    
    .ant-upload-hint {
      color: ${token.colorTextTertiary};
      font-size: ${token.fontSize}px;
    }
  `,
  
  fileList: css`
    margin-top: ${token.marginLG}px;
    
    .ant-list-item {
      padding: ${token.paddingSM}px ${token.padding}px;
      border: 1px solid ${token.colorBorder};
      border-radius: ${token.borderRadius}px;
      margin-bottom: ${token.marginSM}px;
      background: ${token.colorBgContainer};
      
      &:hover {
        border-color: ${token.colorPrimary};
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  `,
  
  fileItem: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  `,
  
  fileInfo: css`
    display: flex;
    align-items: center;
    flex: 1;
    
    .file-icon {
      margin-right: ${token.marginSM}px;
      color: ${token.colorPrimary};
    }
    
    .file-details {
      flex: 1;
      
      .file-name {
        font-weight: 500;
        color: ${token.colorText};
        margin-bottom: 2px;
      }
      
      .file-meta {
        font-size: ${token.fontSizeSM}px;
        color: ${token.colorTextTertiary};
      }
    }
  `,
  
  fileActions: css`
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
  `,
  
  statusTag: css`
    &.success {
      color: ${token.colorSuccess};
      background: ${token.colorSuccessBg};
      border-color: ${token.colorSuccessBorder};
    }
    
    &.error {
      color: ${token.colorError};
      background: ${token.colorErrorBg};
      border-color: ${token.colorErrorBorder};
    }
    
    &.uploading {
      color: ${token.colorWarning};
      background: ${token.colorWarningBg};
      border-color: ${token.colorWarningBorder};
    }
  `,
}));

interface FileUploadProps {
  options?: FileUploadOptions;
  onFilesChange?: (files: FileInfo[]) => void;
  showFileList?: boolean;
  multiple?: boolean;
  disabled?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  options = {},
  onFilesChange,
  showFileList = true,
  multiple = true,
  disabled = false,
}) => {
  const { styles } = useStyles();
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileInfo | null>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon size={20} />;
    } else if (fileType.startsWith('text/') || fileType === 'application/json') {
      return <FileText size={20} />;
    } else {
      return <File size={20} />;
    }
  };

  // 处理文件上传
  const handleUpload = useCallback(async (fileList: File[]) => {
    if (disabled) return;
    
    setUploading(true);
    try {
      const uploadedFiles = await fileUploadService.uploadFiles(fileList, options);
      const newFiles = [...files, ...uploadedFiles];
      setFiles(newFiles);
      onFilesChange?.(newFiles);
      
      const successCount = uploadedFiles.filter(f => f.status === 'success').length;
      if (successCount > 0) {
        message.success(`成功上传 ${successCount} 个文件`);
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '上传失败');
    } finally {
      setUploading(false);
    }
  }, [files, options, onFilesChange, disabled]);

  // 删除文件
  const handleDeleteFile = (fileId: string) => {
    fileUploadService.deleteFile(fileId);
    const newFiles = files.filter(f => f.id !== fileId);
    setFiles(newFiles);
    onFilesChange?.(newFiles);
    message.success('文件删除成功');
  };

  // 预览文件
  const handlePreviewFile = (file: FileInfo) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  // 下载文件内容
  const handleDownloadFile = (file: FileInfo) => {
    if (file.content) {
      const blob = new Blob([file.content], { type: file.type });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  // 自定义上传逻辑
  const customRequest = ({ file, onSuccess, onError }: { file: any; onSuccess?: (response: any) => void; onError?: (error: any) => void }) => {
    handleUpload([file as File])
      .then(() => onSuccess?.({}))
      .catch((error) => onError?.(error));
  };

  return (
    <div className={styles.container}>
      <Dragger
        name="files"
        multiple={multiple}
        customRequest={customRequest}
        showUploadList={false}
        disabled={disabled || uploading}
        accept={options.allowedTypes?.join(',')}
      >
        <p className="ant-upload-drag-icon">
          <UploadIcon size={48} />
        </p>
        <p className="ant-upload-text">
          点击或拖拽文件到此区域上传
        </p>
        <p className="ant-upload-hint">
          {options.allowedTypes 
            ? `支持格式: ${options.allowedTypes.map(type => type.split('/')[1]).join(', ')}`
            : '支持多种文件格式'
          }
          {options.maxSize && ` | 最大 ${formatFileSize(options.maxSize)}`}
        </p>
      </Dragger>

      {showFileList && files.length > 0 && (
        <div className={styles.fileList}>
          <List
            dataSource={files}
            renderItem={(file) => (
              <List.Item className={styles.fileItem}>
                <div className={styles.fileInfo}>
                  <div className="file-icon">
                    {getFileIcon(file.type)}
                  </div>
                  <div className="file-details">
                    <div className="file-name">{file.name}</div>
                    <div className="file-meta">
                      {formatFileSize(file.size)} • {file.type}
                    </div>
                  </div>
                </div>
                
                <Space>
                  <Tag className={`${styles.statusTag} ${file.status}`}>
                    {file.status === 'success' ? '成功' : 
                     file.status === 'error' ? '失败' : '上传中'}
                  </Tag>
                  
                  {file.status === 'success' && (
                    <>
                      {(file.content || file.url) && (
                        <Tooltip title="预览">
                          <Button
                            type="text"
                            size="small"
                            icon={<Eye size={14} />}
                            onClick={() => handlePreviewFile(file)}
                          />
                        </Tooltip>
                      )}
                      
                      {file.content && (
                        <Tooltip title="下载">
                          <Button
                            type="text"
                            size="small"
                            icon={<Download size={14} />}
                            onClick={() => handleDownloadFile(file)}
                          />
                        </Tooltip>
                      )}
                    </>
                  )}
                  
                  <Tooltip title="删除">
                    <Button
                      type="text"
                      size="small"
                      icon={<Trash2 size={14} />}
                      danger
                      onClick={() => handleDeleteFile(file.id)}
                    />
                  </Tooltip>
                </Space>
              </List.Item>
            )}
          />
        </div>
      )}

      {/* 文件预览弹窗 */}
      <Modal
        title={`预览: ${previewFile?.name}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        {previewFile && (
          <div>
            {previewFile.url && previewFile.type.startsWith('image/') ? (
              <Image
                src={previewFile.url}
                alt={previewFile.name}
                style={{ maxWidth: '100%' }}
              />
            ) : previewFile.content ? (
              <pre style={{ 
                whiteSpace: 'pre-wrap', 
                maxHeight: '400px', 
                overflow: 'auto',
                background: '#f5f5f5',
                padding: '16px',
                borderRadius: '6px',
              }}>
                {previewFile.content}
              </pre>
            ) : (
              <div>无法预览此文件类型</div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FileUpload;

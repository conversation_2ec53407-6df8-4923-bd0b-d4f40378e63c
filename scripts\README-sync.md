# 数据库同步脚本使用说明

## 概述

这些脚本用于将本地数据库的数据同步到远程Aiven数据库。提供了两个版本：

1. **完整版本** (`sync-to-aiven.js`) - 功能完整，包含备份和交互确认
2. **快速版本** (`quick-sync.js`) - 简化版本，适合快速同步

## 前置要求

1. 确保已安装Node.js和npm
2. 确保项目依赖已安装：
   ```bash
   npm install mysql2 dotenv
   ```
3. 确保本地数据库连接正常
4. 确保网络可以访问Aiven数据库

## 使用方法

### 1. 完整版本同步 (推荐)

```bash
# 运行完整同步脚本
node scripts/sync-to-aiven.js
```

**功能特点：**
- 交互式确认操作
- 自动创建远程数据库备份
- 按表依赖关系顺序同步
- 详细的进度显示
- 错误处理和回滚

**操作流程：**
1. 脚本会询问是否确认同步
2. 询问是否创建备份
3. 连接到本地和远程数据库
4. 创建备份（如果选择）
5. 逐表同步数据
6. 显示同步结果

### 2. 快速版本同步

```bash
# 同步所有表
node scripts/quick-sync.js

# 同步单个表
node scripts/quick-sync.js users
node scripts/quick-sync.js agents
```

**功能特点：**
- 无交互确认，直接执行
- 不创建备份
- 支持单表同步
- 适合开发环境快速测试

## 配置说明

### 本地数据库配置

脚本会自动读取项目的`.env`文件中的配置：

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=story_ai
```

### 远程数据库配置

远程Aiven数据库配置已硬编码在脚本中：

```javascript
{
  host: 'mysql-6eeec90-vankay966-ba73.b.aivencloud.com',
  port: 24287,
  user: 'avnadmin',
  password: 'AVNS_M5iZ4MKAD4iM98BD6PU',
  database: 'defaultdb'
}
```

## 同步的表

脚本会按以下顺序同步表（考虑外键依赖关系）：

1. `users` - 用户表
2. `agents` - 智能体表
3. `ai_provider_configs` - AI提供商配置
4. `agent_usage_logs` - 使用记录
5. `agent_subscriptions` - 订阅记录
6. `agent_pricing` - 定价配置
7. `redemption_codes` - 兑换码
8. `user_redemptions` - 用户兑换记录
9. `user_settings` - 用户设置
10. `chat_sessions` - 聊天会话
11. `chat_messages` - 聊天消息

## 安全注意事项

⚠️ **重要警告：**

1. **数据覆盖**：同步会完全覆盖远程数据库的数据
2. **备份重要**：建议在同步前创建备份
3. **网络安全**：确保在安全的网络环境下执行
4. **权限控制**：确保只有授权人员可以执行同步

## 故障排除

### 常见错误

1. **连接失败**
   ```
   Error: connect ECONNREFUSED
   ```
   - 检查网络连接
   - 确认数据库配置正确
   - 检查防火墙设置

2. **权限错误**
   ```
   Error: Access denied for user
   ```
   - 检查数据库用户名和密码
   - 确认用户有足够权限

3. **表不存在**
   ```
   Error: Table doesn't exist
   ```
   - 确保本地数据库表结构完整
   - 检查表名是否正确

### 调试模式

如需调试，可以修改脚本添加更多日志：

```javascript
// 在脚本开头添加
process.env.DEBUG = 'true';
```

## 备份恢复

如果同步后需要恢复数据，可以使用备份文件：

1. 备份文件位置：`backups/` 目录
2. 文件格式：JSON格式，每个表一个文件
3. 恢复方法：手动导入或编写恢复脚本

## 性能优化

对于大量数据的同步，可以调整以下参数：

1. **批处理大小**：修改`batchSize`变量
2. **并发连接**：增加数据库连接池大小
3. **网络超时**：调整连接超时设置

## 示例用法

```bash
# 1. 完整同步（生产环境推荐）
node scripts/sync-to-aiven.js

# 2. 快速同步所有表（开发环境）
node scripts/quick-sync.js

# 3. 同步特定表
node scripts/quick-sync.js users
node scripts/quick-sync.js agents

# 4. 检查脚本语法
node -c scripts/sync-to-aiven.js
```

## 联系支持

如果遇到问题，请检查：
1. 网络连接状态
2. 数据库配置信息
3. 本地数据库数据完整性
4. 远程数据库访问权限

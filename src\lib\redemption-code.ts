import crypto from 'crypto';

// 兑换码类型
export type RedemptionCodeType = 'duration' | 'usage';

// 兑换码接口
export interface RedemptionCode {
  id: string;
  agentId: string;
  code: string;
  codeType: RedemptionCodeType;
  label: string;
  durationDays?: number;
  usageCount?: number;
  hashCode: string;
  salt: string;
  isUsed: boolean;
  usedAt?: string;
  usedBy?: string;
  expiresAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  batchId?: string;
}

// 兑换码批次接口
export interface RedemptionBatch {
  id: string;
  agentId: string;
  batchName: string;
  codeType: RedemptionCodeType;
  label: string;
  totalCount: number;
  usedCount: number;
  durationDays?: number;
  usageCount?: number;
  expiresAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 生成随机盐值
export function generateSalt(): string {
  return crypto.randomBytes(16).toString('hex');
}

// 生成兑换码
export function generateRedemptionCode(prefix: string = 'RC'): string {
  // 生成8位随机字符串（大写字母和数字，排除容易混淆的字符）
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let code = '';
  
  for (let i = 0; i < 8; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  // 格式：RC-XXXX-XXXX
  return `${prefix}-${code.substring(0, 4)}-${code.substring(4, 8)}`;
}

// 创建兑换码哈希
export function createCodeHash(code: string, salt: string): string {
  return crypto.createHash('sha256').update(code + salt).digest('hex');
}

// 验证兑换码
export function verifyCode(code: string, hash: string, salt: string): boolean {
  const computedHash = createCodeHash(code, salt);
  return computedHash === hash;
}

// 生成批量兑换码
export function generateBatchCodes(
  count: number,
  agentId: string,
  codeType: RedemptionCodeType,
  label: string,
  durationDays?: number,
  usageCount?: number,
  expiresAt?: Date
): {
  codes: Array<{
    code: string;
    salt: string;
    hashCode: string;
  }>;
  batchInfo: {
    agentId: string;
    codeType: RedemptionCodeType;
    label: string;
    totalCount: number;
    durationDays?: number;
    usageCount?: number;
    expiresAt?: string;
  };
} {
  const codes = [];
  const usedCodes = new Set<string>();
  
  // 生成指定数量的唯一兑换码
  for (let i = 0; i < count; i++) {
    let code: string;
    let attempts = 0;
    const maxAttempts = 100;
    
    // 确保生成的兑换码是唯一的
    do {
      code = generateRedemptionCode();
      attempts++;
      
      if (attempts > maxAttempts) {
        throw new Error('无法生成足够的唯一兑换码，请减少数量或稍后重试');
      }
    } while (usedCodes.has(code));
    
    usedCodes.add(code);
    
    const salt = generateSalt();
    const hashCode = createCodeHash(code, salt);
    
    codes.push({
      code,
      salt,
      hashCode,
    });
  }
  
  return {
    codes,
    batchInfo: {
      agentId,
      codeType,
      label,
      totalCount: count,
      durationDays,
      usageCount,
      expiresAt: expiresAt?.toISOString(),
    },
  };
}

// 验证兑换码格式
export function validateCodeFormat(code: string): boolean {
  // 验证格式：RC-XXXX-XXXX
  const pattern = /^RC-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
  return pattern.test(code);
}

// 检查兑换码是否过期
export function isCodeExpired(expiresAt?: string): boolean {
  if (!expiresAt) return false;
  return new Date() > new Date(expiresAt);
}

// 格式化兑换码用于显示
export function formatCodeForDisplay(code: string): string {
  return code.replace(/-/g, ' - ');
}

// 生成Excel导出数据
export function generateExcelData(codes: RedemptionCode[], batchInfo: RedemptionBatch) {
  const headers = [
    '兑换码',
    '类型',
    '标签',
    '价值',
    '有效期',
    '状态',
    '创建时间'
  ];
  
  const rows = codes.map(code => [
    code.code,
    code.codeType === 'duration' ? '时长兑换' : '次数兑换',
    code.label,
    code.codeType === 'duration' 
      ? `${code.durationDays}天` 
      : `${code.usageCount}次`,
    code.expiresAt ? new Date(code.expiresAt).toLocaleDateString() : '永久有效',
    code.isUsed ? '已使用' : '未使用',
    new Date(code.createdAt).toLocaleString()
  ]);
  
  return {
    headers,
    rows,
    filename: `兑换码_${batchInfo.label}_${new Date().toISOString().split('T')[0]}.xlsx`
  };
}

'use client';

import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import { 
  Card, 
  Button, 
  Input, 
  Table, 
  Space, 
  Modal, 
  Form, 
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Select,
  Divider
} from 'antd';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Search,
  Star,
  StarOff,
  Download,
  Upload
} from 'lucide-react';
import { Prompt } from '@/types';
import { usePromptsStore } from '@/store/prompts';

const { TextArea } = Input;
const { Option } = Select;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: ${token.marginLG}px;
    padding: ${token.paddingLG}px;
  `,
  
  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${token.marginLG}px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: ${token.marginMD}px;
      
      h2 {
        margin: 0;
        font-size: ${token.fontSizeXL}px;
        font-weight: 600;
      }
    }
    
    .header-right {
      display: flex;
      gap: ${token.marginSM}px;
    }
  `,
  
  searchBar: css`
    display: flex;
    gap: ${token.marginMD}px;
    margin-bottom: ${token.marginLG}px;
    
    .search-input {
      flex: 1;
    }
    
    .category-filter {
      min-width: 150px;
    }
  `,
  
  tableContainer: css`
    flex: 1;
    
    .ant-table-wrapper {
      height: 100%;
      
      .ant-table-container {
        height: 100%;
      }
      
      .ant-table-body {
        height: calc(100% - 55px);
        overflow-y: auto;
      }
    }
  `,
  
  promptContent: css`
    max-width: 300px;
    
    .content-preview {
      max-height: 60px;
      overflow-y: auto;
      font-size: ${token.fontSizeSM}px;
      line-height: 1.4;
      color: ${token.colorTextSecondary};
    }
  `,
  
  categoryTag: css`
    &.system {
      background: ${token.colorPrimaryBg};
      color: ${token.colorPrimary};
      border-color: ${token.colorPrimary};
    }
    
    &.custom {
      background: ${token.colorSuccessBg};
      color: ${token.colorSuccess};
      border-color: ${token.colorSuccess};
    }
    
    &.favorite {
      background: ${token.colorWarningBg};
      color: ${token.colorWarning};
      border-color: ${token.colorWarning};
    }
  `,
  
  favoriteButton: css`
    color: ${token.colorTextTertiary};
    
    &.favorited {
      color: ${token.colorWarning};
    }
    
    &:hover {
      color: ${token.colorWarning};
    }
  `,
}));

const PromptManager: React.FC = () => {
  const { styles } = useStyles();

  // 使用提示词状态管理
  const {
    prompts,
    selectedPrompt,
    loading,
    error,
    fetchPrompts,
    addPrompt,
    updatePrompt,
    deletePrompt,
    duplicatePrompt,
    selectPrompt,
    getCategories,
    searchPrompts,
    exportPrompts,
  } = usePromptsStore();

  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [form] = Form.useForm();

  // 初始化数据
  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  // 获取过滤后的提示词
  const getFilteredPrompts = () => {
    let filtered = prompts;

    if (searchText) {
      filtered = searchPrompts(searchText);
    }

    if (categoryFilter !== 'all') {
      if (categoryFilter === 'favorite') {
        filtered = filtered.filter(prompt => prompt.isFavorite);
      } else {
        filtered = filtered.filter(prompt => prompt.category === categoryFilter);
      }
    }

    return filtered;
  };

  const filteredPrompts = getFilteredPrompts();

  // 搜索和筛选
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleCategoryFilter = (category: string) => {
    setCategoryFilter(category);
  };

  // 添加/编辑提示词
  const handleAddPrompt = () => {
    setEditingPrompt(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPrompt = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    form.setFieldsValue(prompt);
    setIsModalVisible(true);
  };

  const handleSavePrompt = async () => {
    try {
      const values = await form.validateFields();

      if (editingPrompt) {
        // 编辑现有提示词 - 确保包含所有必需字段
        const updateData = {
          title: values.title,
          content: values.content,
          category: values.category || 'custom',
          tags: values.tags || [],
          isFavorite: editingPrompt.isFavorite, // 保持原有的收藏状态
        };
        updatePrompt(editingPrompt.id, updateData);
        message.success('提示词更新成功');
      } else {
        // 添加新提示词
        addPrompt(values);
        message.success('提示词添加成功');
      }

      setIsModalVisible(false);
      setEditingPrompt(null);
      form.resetFields();
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 删除提示词
  const handleDeletePrompt = (id: string) => {
    deletePrompt(id);
    message.success('提示词删除成功');
  };

  // 切换收藏状态
  const toggleFavorite = (id: string) => {
    const prompt = prompts.find(p => p.id === id);
    if (prompt) {
      updatePrompt(id, {
        title: prompt.title,
        content: prompt.content,
        category: prompt.category,
        tags: prompt.tags,
        isFavorite: !prompt.isFavorite
      });
    }
  };

  // 复制提示词
  const handleCopyPrompt = (prompt: Prompt) => {
    navigator.clipboard.writeText(prompt.content);
    message.success('提示词已复制到剪贴板');
  };



  // 导出提示词
  const handleExportPrompts = () => {
    exportPrompts();
    message.success('提示词导出成功');
  };

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string, record: Prompt) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={record.isFavorite ? <Star size={14} /> : <StarOff size={14} />}
            className={`${styles.favoriteButton} ${record.isFavorite ? 'favorited' : ''}`}
            onClick={() => toggleFavorite(record.id)}
          />
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Space>
      ),
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => (
        <div className={styles.promptContent}>
          <div className="content-preview" title={text}>
            {text}
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => (
        <Tag className={`${styles.categoryTag} ${category}`}>
          {category === 'system' && '系统'}
          {category === 'custom' && '自定义'}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <Space wrap>
          {tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: unknown, record: Prompt) => (
        <Space size="small">
          <Tooltip title="复制">
            <Button
              type="text"
              size="small"
              icon={<Copy size={14} />}
              onClick={() => handleCopyPrompt(record)}
            />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<Edit size={14} />}
              onClick={() => handleEditPrompt(record)}
            />
          </Tooltip>
          
          {record.category === 'custom' && (
            <Popconfirm
              title="确定要删除这个提示词吗？"
              onConfirm={() => handleDeletePrompt(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  icon={<Trash2 size={14} />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 头部 */}
      <div className={styles.header}>
        <div className="header-left">
          <h2>提示词管理</h2>
          <Tag color="blue">{filteredPrompts.length} 个提示词</Tag>
        </div>
        <div className="header-right">
          <Button
            icon={<Download size={16} />}
            onClick={handleExportPrompts}
          >
            导出
          </Button>
          <Button
            type="primary"
            icon={<Plus size={16} />}
            onClick={handleAddPrompt}
          >
            添加提示词
          </Button>
        </div>
      </div>

      {/* 搜索栏 */}
      <div className={styles.searchBar}>
        <Input
          className="search-input"
          placeholder="搜索提示词标题、内容或标签..."
          prefix={<Search size={16} />}
          value={searchText}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
        <Select
          className="category-filter"
          value={categoryFilter}
          onChange={handleCategoryFilter}
        >
          <Option value="all">全部分类</Option>
          <Option value="favorite">收藏夹</Option>
          {getCategories().map(category => (
            <Option key={category} value={category}>{category}</Option>
          ))}
        </Select>
      </div>

      {/* 提示词表格 */}
      <Card className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={filteredPrompts}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个提示词`,
          }}
          size="small"
        />
      </Card>

      {/* 添加/编辑弹窗 */}
      <Modal
        title={editingPrompt ? '编辑提示词' : '添加提示词'}
        open={isModalVisible}
        onOk={handleSavePrompt}
        onCancel={() => setIsModalVisible(false)}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            category: 'custom',
            tags: [],
          }}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入提示词标题' }]}
          >
            <Input placeholder="请输入提示词标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入提示词内容' }]}
          >
            <TextArea
              rows={8}
              placeholder="请输入提示词内容，可以使用 {input} 作为占位符"
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select>
              <Option value="custom">自定义</Option>
              <Option value="system">系统</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PromptManager;
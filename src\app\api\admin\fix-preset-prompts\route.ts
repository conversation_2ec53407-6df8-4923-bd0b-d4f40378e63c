import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// POST - 修复preset_prompts字段的JSON格式
export async function POST(request: NextRequest) {
  try {
    console.log('开始修复preset_prompts字段的JSON格式...');

    // 1. 查看当前有问题的数据
    const problemDataQuery = `
      SELECT id, name, preset_prompts 
      FROM agents 
      WHERE preset_prompts IS NOT NULL 
        AND preset_prompts != 'null' 
        AND preset_prompts != '[]'
        AND preset_prompts != '""'
    `;

    const problemData = await executeQuery<any[]>(problemDataQuery);
    console.log('发现有问题的数据:', problemData.length, '条');
    
    problemData.forEach(row => {
      console.log(`智能体 ${row.id} (${row.name}): ${row.preset_prompts}`);
    });

    // 2. 直接替换所有单引号为双引号（更简单直接的方法）
    const fixQuery = `
      UPDATE agents
      SET preset_prompts = REPLACE(preset_prompts, "'", '"')
      WHERE preset_prompts IS NOT NULL
        AND preset_prompts LIKE "%'%"
    `;

    const result1 = await executeQuery(fixQuery);
    console.log('修复所有单引号为双引号，影响行数:', result1.affectedRows || 0);

    // 3. 清理明显无效的数据
    const cleanupQuery = `
      UPDATE agents
      SET preset_prompts = '[]'
      WHERE preset_prompts IS NOT NULL
        AND preset_prompts NOT LIKE '[%]'
        AND preset_prompts != 'null'
    `;

    const result2 = await executeQuery(cleanupQuery);
    console.log('清理无效数据，影响行数:', result2.affectedRows || 0);

    // 6. 验证修复结果
    const validationQuery = `
      SELECT id, name, preset_prompts,
             JSON_VALID(preset_prompts) as is_valid_json
      FROM agents 
      WHERE preset_prompts IS NOT NULL 
        AND preset_prompts != 'null'
    `;

    const validationData = await executeQuery<any[]>(validationQuery);
    console.log('验证修复结果...');
    
    let validCount = 0;
    let invalidCount = 0;
    
    validationData.forEach(row => {
      if (row.is_valid_json === 1) {
        validCount++;
        console.log(`✓ 智能体 ${row.id} (${row.name}): ${row.preset_prompts}`);
      } else {
        invalidCount++;
        console.log(`✗ 智能体 ${row.id} (${row.name}): ${row.preset_prompts}`);
      }
    });

    console.log(`修复完成: ${validCount} 条有效, ${invalidCount} 条仍然无效`);

    return NextResponse.json({
      success: true,
      message: 'preset_prompts字段JSON格式修复完成',
      details: {
        totalProcessed: problemData.length,
        fixedQuotes: result1.affectedRows || 0,
        cleanedUp: result2.affectedRows || 0,
        finalValid: validCount,
        finalInvalid: invalidCount
      }
    });
  } catch (error) {
    console.error('修复preset_prompts字段失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '修复preset_prompts字段失败',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

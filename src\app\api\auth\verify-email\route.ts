import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// POST - 验证邮箱
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: '验证token不能为空',
        },
        { status: 400 }
      );
    }

    // 查找验证token
    const users = await executeQuery(
      'SELECT id, username, email, verification_expires_at FROM users WHERE verification_token = ?',
      [token]
    );

    if (users.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '无效的验证链接',
        },
        { status: 400 }
      );
    }

    const user = users[0];

    // 检查token是否过期
    const now = new Date();
    const expiresAt = new Date(user.verification_expires_at);
    
    if (now > expiresAt) {
      return NextResponse.json(
        {
          success: false,
          error: '验证链接已过期，请重新发送验证邮件',
        },
        { status: 400 }
      );
    }

    // 更新用户验证状态
    await executeQuery(
      'UPDATE users SET is_verified = ?, verification_token = NULL, verification_expires_at = NULL WHERE id = ?',
      [true, user.id]
    );

    return NextResponse.json({
      success: true,
      message: '邮箱验证成功，现在可以登录了',
      data: {
        username: user.username,
        email: user.email,
      },
    });
  } catch (error) {
    console.error('邮箱验证失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '验证失败，请重试',
      },
      { status: 500 }
    );
  }
}

// GET - 通过URL参数验证邮箱
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: '验证token不能为空',
        },
        { status: 400 }
      );
    }

    // 查找验证token
    const users = await executeQuery(
      'SELECT id, username, email, verification_expires_at FROM users WHERE verification_token = ?',
      [token]
    );

    if (users.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '无效的验证链接',
        },
        { status: 400 }
      );
    }

    const user = users[0];

    // 检查token是否过期
    const now = new Date();
    const expiresAt = new Date(user.verification_expires_at);
    
    if (now > expiresAt) {
      return NextResponse.json(
        {
          success: false,
          error: '验证链接已过期，请重新发送验证邮件',
        },
        { status: 400 }
      );
    }

    // 更新用户验证状态
    await executeQuery(
      'UPDATE users SET is_verified = ?, verification_token = NULL, verification_expires_at = NULL WHERE id = ?',
      [true, user.id]
    );

    // 重定向到登录页面，带成功消息
    return NextResponse.redirect(new URL('/login?verified=true', request.url));
  } catch (error) {
    console.error('邮箱验证失败:', error);
    return NextResponse.redirect(new URL('/login?error=verification_failed', request.url));
  }
}

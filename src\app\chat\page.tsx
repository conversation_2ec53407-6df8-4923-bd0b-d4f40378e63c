'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createStyles } from 'antd-style';
import { message } from 'antd';
import { Flexbox } from 'react-layout-kit';
import { useChatStore } from '@/store/chat';
import { useAuthStore } from '@/store/auth';
import MainSideBar from '@/components/layout/MainSideBar';
import CurrentSessionPanel from '@/components/chat/CurrentSessionPanel';
import HistoryPanel from '@/components/chat/HistoryPanel';
import Workspace from '@/components/chat/Workspace';
import ChatList from '@/components/chat/ChatList';
import ChatInputNew from '@/components/chat/ChatInputNew';
import LoadingSpinner from '@/components/common/LoadingSpinner';

const useStyles = createStyles(({ token, css }) => ({
  layout: css`
    height: 100vh;
    display: flex;
    background: ${token.colorBgLayout};
    max-width: 100%;
    overflow: hidden;
    position: relative;
  `,
}));





const ChatPage: React.FC = () => {
  const { styles } = useStyles();
  const router = useRouter();
  const { checkLoginStatus } = useAuthStore();
  const { sendMessage, isLoading } = useChatStore();
  const [mounted, setMounted] = useState(false);

  // 检查登录状态
  useEffect(() => {
    const initAuth = async () => {
      await checkLoginStatus();

      const currentState = useAuthStore.getState();
      if (!currentState.isLoggedIn) {
        router.replace('/login');
        return;
      }
      setMounted(true);
    };

    initAuth();
  }, [router, checkLoginStatus]);

  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(content);
    } catch (error) {
      message.error('发送消息失败，请重试');
    }
  };




  // 如果还未挂载完成，显示加载状态
  if (!mounted) {
    return <LoadingSpinner />;
  }

  return (
    <div className={styles.layout}>
      {/* 最左边的主导航侧边栏 */}
      <MainSideBar />

      {/* 当前会话面板 */}
      <CurrentSessionPanel />

      {/* 主工作区 */}
      <Flexbox
        flex={1}
        horizontal
        style={{ overflow: 'hidden', position: 'relative' }}
      >
        {/* 聊天区域 */}
        <Flexbox
          flex={1}
          style={{ overflow: 'hidden', position: 'relative' }}
        >
          <Workspace>
            <ChatList />
            <ChatInputNew
              onSend={handleSendMessage}
              loading={isLoading}
            />
          </Workspace>
        </Flexbox>

        {/* 右侧历史会话面板 */}
        <HistoryPanel />
      </Flexbox>
    </div>
  );
};

export default ChatPage;
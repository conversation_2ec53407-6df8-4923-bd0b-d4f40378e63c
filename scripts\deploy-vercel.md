# Vercel 部署指南

## 1. 准备工作

### 1.1 数据库准备
由于 Vercel 不支持 MySQL 数据库，你需要使用云数据库服务：

**推荐选项：**
- **PlanetScale** (免费额度，MySQL 兼容)
- **Railway** (免费额度，支持 MySQL)
- **Supabase** (免费额度，PostgreSQL，需要修改代码)
- **阿里云 RDS** (付费，稳定)
- **腾讯云 MySQL** (付费，稳定)

### 1.2 环境变量配置
在 Vercel 项目设置中添加以下环境变量：

```
DB_HOST=your-cloud-database-host
DB_PORT=3306
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=story_ai
NEXT_PUBLIC_API_URL=https://your-project.vercel.app
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
ENCRYPTION_KEY=your-32-character-secret-key
```

## 2. 部署步骤

### 2.1 通过 Vercel CLI 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel

# 部署到生产环境
vercel --prod
```

### 2.2 通过 GitHub 集成部署
1. 将代码推送到 GitHub
2. 在 Vercel 控制台导入 GitHub 项目
3. 配置环境变量
4. 点击部署

## 3. 数据库初始化

部署完成后，需要在云数据库中执行初始化脚本：

```sql
-- 连接到你的云数据库，执行以下脚本
-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS story_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 执行 scripts/init-db.sql 中的所有表结构
-- 3. 执行其他必要的 SQL 脚本
```

## 4. 注意事项

### 4.1 文件上传
Vercel 的无服务器函数不支持持久化文件存储，如果需要文件上传功能，建议：
- 使用 Vercel Blob Storage
- 使用 AWS S3
- 使用阿里云 OSS

### 4.2 函数超时
Vercel 免费版函数执行时间限制为 10 秒，Pro 版为 60 秒。
如果有长时间运行的任务，考虑：
- 优化代码性能
- 使用后台任务队列
- 升级到 Pro 版本

### 4.3 冷启动
无服务器函数可能存在冷启动延迟，可以通过以下方式优化：
- 减少依赖包大小
- 使用 Vercel Edge Functions
- 实现预热机制

## 5. 监控和调试

### 5.1 查看日志
```bash
vercel logs your-project-url
```

### 5.2 本地调试
```bash
vercel dev
```

## 6. 域名配置

### 6.1 自定义域名
1. 在 Vercel 项目设置中添加域名
2. 配置 DNS 记录指向 Vercel
3. 等待 SSL 证书自动配置

### 6.2 更新环境变量
部署后记得更新 `NEXT_PUBLIC_API_URL` 为你的实际域名。

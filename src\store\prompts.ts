import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Prompt, PromptsState } from '@/types';
import { promptsApi } from '@/lib/api';

interface PromptsStore extends PromptsState {
  // Actions
  fetchPrompts: () => Promise<void>;
  addPrompt: (prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updatePrompt: (id: string, updates: Partial<Prompt>) => Promise<void>;
  deletePrompt: (id: string) => Promise<void>;
  duplicatePrompt: (id: string) => Promise<void>;
  selectPrompt: (prompt: Prompt | null) => void;

  // Categories
  getCategories: () => string[];
  getPromptsByCategory: (category: string) => Prompt[];

  // Search and filter
  searchPrompts: (query: string) => Prompt[];
  filterPrompts: (filters: { category?: string; tags?: string[] }) => Prompt[];

  // Import/Export
  importPrompts: (prompts: Prompt[]) => Promise<void>;
  exportPrompts: () => void;

  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 生成唯一 ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 默认提示词
const DEFAULT_PROMPTS: Prompt[] = [
  {
    id: 'writing-assistant',
    title: '写作助手',
    content: '请帮我改进以下文本的表达和结构，使其更加清晰、流畅和有说服力：\n\n{text}',
    description: '帮助改进文本的表达和结构',
    category: '写作',
    tags: ['写作', '改进', '润色'],
    isFavorite: false,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'code-reviewer',
    title: '代码审查',
    content: '请审查以下代码，指出潜在的问题、改进建议和最佳实践：\n\n```{language}\n{code}\n```\n\n请从以下方面进行分析：\n1. 代码质量和可读性\n2. 性能优化\n3. 安全性\n4. 最佳实践',
    description: '专业的代码审查和改进建议',
    category: '编程',
    tags: ['代码', '审查', '优化'],
    isFavorite: false,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'translator',
    title: '专业翻译',
    content: '请将以下{source_language}文本翻译成{target_language}，要求：\n1. 保持原文的语气和风格\n2. 确保术语的准确性\n3. 符合目标语言的表达习惯\n\n原文：\n{text}',
    description: '专业的多语言翻译服务',
    category: '翻译',
    tags: ['翻译', '多语言', '专业'],
    isFavorite: false,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'business-email',
    title: '商务邮件',
    content: '请帮我写一封{tone}的商务邮件，内容如下：\n\n收件人：{recipient}\n主题：{subject}\n主要内容：{content}\n\n要求：\n1. 语言正式且礼貌\n2. 结构清晰\n3. 突出重点\n4. 包含适当的开头和结尾',
    description: '生成专业的商务邮件',
    category: '商务',
    tags: ['邮件', '商务', '沟通'],
    isFavorite: false,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'social-media',
    title: '社交媒体',
    content: '请为{platform}平台创建一个关于{topic}的帖子，要求：\n\n1. 吸引人的开头\n2. 简洁有力的内容\n3. 包含相关话题标签\n4. 鼓励互动的结尾\n\n目标受众：{audience}\n语调：{tone}',
    description: '创建吸引人的社交媒体内容',
    category: '营销',
    tags: ['社交媒体', '营销', '内容创作'],
    isFavorite: false,
    isPublic: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const usePromptsStore = create<PromptsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      prompts: [],
      selectedPrompt: null,
      loading: false,
      error: null,

      // 获取提示词列表
      fetchPrompts: async () => {
        set({ loading: true, error: null });
        try {
          const result = await promptsApi.getAll();
          if (result.success && result.data) {
            set({ prompts: result.data, loading: false });
          } else {
            set({ error: result.error || '获取提示词失败', loading: false });
          }
        } catch (error) {
          set({ error: '获取提示词失败', loading: false });
        }
      },

      // Prompt management
      addPrompt: async (prompt) => {
        set({ loading: true, error: null });
        try {
          const result = await promptsApi.create(prompt);
          if (result.success && result.data) {
            set((state) => ({
              prompts: [...state.prompts, result.data!],
              loading: false,
            }));
          } else {
            set({ error: result.error || '创建提示词失败', loading: false });
          }
        } catch (error) {
          set({ error: '创建提示词失败', loading: false });
        }
      },

      updatePrompt: async (id, updates) => {
        set({ loading: true, error: null });
        try {
          const result = await promptsApi.update(id, updates);
          if (result.success) {
            set((state) => ({
              prompts: state.prompts.map(prompt =>
                prompt.id === id
                  ? { ...prompt, ...updates, updatedAt: new Date().toISOString() }
                  : prompt
              ),
              loading: false,
            }));
          } else {
            set({ error: result.error || '更新提示词失败', loading: false });
          }
        } catch (error) {
          set({ error: '更新提示词失败', loading: false });
        }
      },

      deletePrompt: async (id) => {
        set({ loading: true, error: null });
        try {
          const result = await promptsApi.delete(id);
          if (result.success) {
            set((state) => ({
              prompts: state.prompts.filter(prompt => prompt.id !== id),
              selectedPrompt: state.selectedPrompt?.id === id ? null : state.selectedPrompt,
              loading: false,
            }));
          } else {
            set({ error: result.error || '删除提示词失败', loading: false });
          }
        } catch (error) {
          set({ error: '删除提示词失败', loading: false });
        }
      },

      duplicatePrompt: async (id) => {
        set({ loading: true, error: null });
        try {
          const { prompts } = get();
          const originalPrompt = prompts.find(p => p.id === id);
          if (originalPrompt) {
            const duplicatedPrompt = {
              title: `${originalPrompt.title} (副本)`,
              content: originalPrompt.content,
              description: originalPrompt.description,
              category: originalPrompt.category,
              tags: originalPrompt.tags,
              isFavorite: originalPrompt.isFavorite,
              isPublic: originalPrompt.isPublic,
            };

            const result = await promptsApi.create(duplicatedPrompt);
            if (result.success && result.data) {
              set((state) => ({
                prompts: [...state.prompts, result.data!],
                loading: false,
              }));
            } else {
              set({ error: result.error || '复制提示词失败', loading: false });
            }
          } else {
            set({ error: '原提示词不存在', loading: false });
          }
        } catch (error) {
          set({ error: '复制提示词失败', loading: false });
        }
      },

      selectPrompt: (prompt) => {
        set({ selectedPrompt: prompt });
      },

      // Categories
      getCategories: () => {
        const { prompts } = get();
        const categories = [...new Set(prompts.map(p => p.category))];
        return categories.sort();
      },

      getPromptsByCategory: (category) => {
        const { prompts } = get();
        return prompts.filter(p => p.category === category);
      },

      // Search and filter
      searchPrompts: (query) => {
        const { prompts } = get();
        const lowercaseQuery = query.toLowerCase();
        return prompts.filter(prompt =>
          prompt.title.toLowerCase().includes(lowercaseQuery) ||
          (prompt.description && prompt.description.toLowerCase().includes(lowercaseQuery)) ||
          prompt.content.toLowerCase().includes(lowercaseQuery) ||
          prompt.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
        );
      },

      filterPrompts: (filters) => {
        const { prompts } = get();
        return prompts.filter(prompt => {
          if (filters.category && prompt.category !== filters.category) {
            return false;
          }
          if (filters.tags && filters.tags.length > 0) {
            return filters.tags.some(tag => prompt.tags?.includes(tag));
          }
          return true;
        });
      },

      // Import/Export
      importPrompts: async (importedPrompts) => {
        set({ loading: true, error: null });
        try {
          const results = await Promise.all(
            importedPrompts.map(prompt =>
              promptsApi.create({
                title: prompt.title,
                content: prompt.content,
                description: prompt.description,
                category: prompt.category,
                tags: prompt.tags,
                isFavorite: prompt.isFavorite,
                isPublic: prompt.isPublic,
              })
            )
          );

          const successfulPrompts = results
            .filter(result => result.success && result.data)
            .map(result => result.data!);

          set((state) => ({
            prompts: [...state.prompts, ...successfulPrompts],
            loading: false,
          }));
        } catch (error) {
          set({ error: '导入提示词失败', loading: false });
        }
      },

      exportPrompts: () => {
        const { prompts } = get();
        const exportData = {
          prompts,
          exportTime: new Date().toISOString(),
          version: '1.0',
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
          type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompts-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
      },

      // State management
      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },
    }),
    {
      name: 'prompts-storage',
      partialize: (state) => ({
        prompts: state.prompts,
      }),
    }
  )
);

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Message, ChatSession, FileInfo, AIProvider } from '@/types';
import { aiService } from '@/services/aiService';
import { useAgentStore } from '@/store/agent';
import { useAIProviderConfigsStore } from '@/store/aiProviderConfigs';
import { agentUsageApi, agentRemainingApi, agentStatusApi } from '@/lib/api';
import { useAuthStore } from '@/store/auth';

interface ChatStore {
  // 当前活跃的会话列表（可以有多个）
  currentSessions: ChatSession[];

  // 历史会话列表
  historySessions: ChatSession[];

  // 当前选中的会话ID
  activeSessionId: string | null;

  // 消息发送状态
  isLoading: boolean;

  // 当前输入的文件
  uploadedFiles: FileInfo[];

  // 当前会话智能体状态
  currentAgentStatus: {
    agentId: string | null;
    exists: boolean;
    enabled: boolean;
    name: string | null;
    reason: string | null;
  } | null;

  // 中断控制器，用于停止生成
  abortController: AbortController | null;

  // Actions
  createSession: (options?: { title?: string; provider?: AIProvider; model?: string; agentId?: string; agentName?: string; agentDescription?: string; agentCategory?: string }) => string;
  switchToSession: (sessionUniqueId: string) => void;
  deleteSession: (sessionUniqueId: string) => void;
  updateSessionTitle: (sessionUniqueId: string, title: string) => void;
  updateSessionModel: (sessionUniqueId: string, provider: AIProvider, model: string) => void;
  
  sendMessage: (content: string, files?: FileInfo[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  deleteMessage: (messageId: string) => void;
  regenerateMessage: (messageId: string) => void;
  clearSessionMessages: () => void;
  clearHistorySessions: () => void;
  checkAgentStatus: (agentId: string) => Promise<void>;

  setLoading: (loading: boolean) => void;
  setUploadedFiles: (files: FileInfo[]) => void;
  addUploadedFile: (file: FileInfo) => void;
  removeUploadedFile: (fileId: string) => void;
  clearUploadedFiles: () => void;

  // 设置当前消息内容（用于提示词管理）
  setCurrentMessage: (content: string) => void;

  // 清空所有数据
  clearAll: () => void;
}

const generateId = () => Date.now().toString(36) + Math.random().toString(36).substring(2);

// 普通对话的特殊agentId
export const DEFAULT_AGENT_ID = 'default';

interface CreateSessionOptions {
  title?: string;
  provider?: AIProvider;
  model?: string;
  agentId?: string;
  agentName?: string;
  agentDescription?: string;
  agentCategory?: string;
}

const createNewSession = (options?: CreateSessionOptions): ChatSession => {
  const agentId = options?.agentId || DEFAULT_AGENT_ID;

  // 获取默认的AI提供商配置
  let defaultProvider: AIProvider = 'google';
  let defaultModel = 'gemini-2.0-flash';

  try {
    const aiProvidersConfig = JSON.parse(localStorage.getItem('ai-providers-storage') || '{}');
    if (aiProvidersConfig.state) {
      defaultProvider = aiProvidersConfig.state.currentProvider || 'google';
      defaultModel = aiProvidersConfig.state.currentModel || 'gemini-2.0-flash';
    }
  } catch (error) {
    console.warn('Failed to load AI providers config, using defaults');
  }

  // 生成唯一的会话ID
  const sessionUniqueId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // 初始消息为空，智能体描述在用户第一次发送消息时添加
  const initialMessages: Message[] = [];

  return {
    id: agentId, // 会话ID等于智能体ID
    sessionUniqueId: sessionUniqueId, // 新增的唯一会话ID
    title: options?.title || '新对话',
    messages: initialMessages,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    provider: options?.provider || defaultProvider,
    model: options?.model || defaultModel,
    agentId: agentId,
    agentName: options?.agentName,
    agentDescription: options?.agentDescription,
    agentCategory: options?.agentCategory,
  };
};

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      currentSessions: [],
      historySessions: [],
      activeSessionId: null,
      isLoading: false,
      uploadedFiles: [],
      currentAgentStatus: null,
      abortController: null,

      createSession: (options?: CreateSessionOptions) => {
        const agentId = options?.agentId || DEFAULT_AGENT_ID;

        // 检查当前会话中是否已存在该智能体ID的会话
        const existingInCurrent = get().currentSessions.find(session => session.id === agentId);

        if (existingInCurrent) {
          // 如果当前会话中已存在，将其移动到历史会话，然后创建新会话
          set((state) => {
            const newCurrentSessions = state.currentSessions.filter(s => s.id !== agentId);

            // 创建现有会话的深拷贝，确保数据独立
            const existingInCurrentCopy = {
              ...existingInCurrent,
              messages: [...existingInCurrent.messages.map(msg => ({ ...msg }))]
            };
            const newHistorySessions = [existingInCurrentCopy, ...state.historySessions];

            // 创建新会话
            const newSession = createNewSession({
              ...options,
              agentId: agentId
            });

            return {
              currentSessions: [...newCurrentSessions, newSession],
              historySessions: newHistorySessions,
              activeSessionId: newSession.id
            };
          });
          return agentId;
        }

        // 检查历史会话中是否存在该智能体ID的会话
        const existingInHistory = get().historySessions.find(session => session.id === agentId);

        if (existingInHistory) {
          // 将历史会话移到历史，创建新会话
          set((state) => {
            const newHistorySessions = state.historySessions.filter(s => s.id !== agentId);

            // 创建历史会话的深拷贝，确保数据独立
            const existingInHistoryCopy = {
              ...existingInHistory,
              messages: [...existingInHistory.messages.map(msg => ({ ...msg }))]
            };

            // 创建新会话
            const newSession = createNewSession({
              ...options,
              agentId: agentId
            });

            return {
              currentSessions: [...state.currentSessions, newSession],
              historySessions: [existingInHistoryCopy, ...newHistorySessions],
              activeSessionId: newSession.id
            };
          });
          return agentId;
        }

        // 不存在该智能体的会话，直接创建新会话
        const newSession = createNewSession({
          ...options,
          agentId: agentId
        });

        set((state) => ({
          currentSessions: [...state.currentSessions, newSession],
          activeSessionId: newSession.id
        }));

        return newSession.id;
      },

      switchToSession: (sessionUniqueId: string) => {
        // 在历史会话中查找（使用sessionUniqueId进行精确查找）
        const sessionInHistory = get().historySessions.find(s => s.sessionUniqueId === sessionUniqueId);

        if (sessionInHistory) {
          set((state) => {
            // 获取历史会话的agentId（如果没有则使用DEFAULT_AGENT_ID）
            const historyAgentId = sessionInHistory.agentId || DEFAULT_AGENT_ID;

            // 在当前会话中查找相同agentId的会话
            const sameAgentCurrentSession = state.currentSessions.find(s =>
              (s.agentId || DEFAULT_AGENT_ID) === historyAgentId
            );

            if (sameAgentCurrentSession) {
              // 对调：相同agentId的当前会话移到历史，历史会话移到当前并设为活跃
              // 创建深拷贝确保会话数据独立
              const sessionInHistoryCopy = {
                ...sessionInHistory,
                messages: [...sessionInHistory.messages.map(msg => ({ ...msg }))]
              };
              const sameAgentCurrentSessionCopy = {
                ...sameAgentCurrentSession,
                messages: [...sameAgentCurrentSession.messages.map(msg => ({ ...msg }))]
              };

              const newCurrentSessions = state.currentSessions.map(s =>
                s.sessionUniqueId === sameAgentCurrentSession.sessionUniqueId ? sessionInHistoryCopy : s
              );
              const newHistorySessions = state.historySessions.map(s =>
                s.sessionUniqueId === sessionInHistory.sessionUniqueId ? sameAgentCurrentSessionCopy : s
              );

              return {
                currentSessions: newCurrentSessions,
                historySessions: newHistorySessions,
                activeSessionId: sessionInHistoryCopy.id // 使用agentId作为activeSessionId
              };
            } else {
              // 如果没有相同agentId的当前会话，直接将历史会话移到当前会话
              // 创建深拷贝确保会话数据独立
              const sessionInHistoryCopy = {
                ...sessionInHistory,
                messages: [...sessionInHistory.messages.map(msg => ({ ...msg }))]
              };
              const newCurrentSessions = [...state.currentSessions, sessionInHistoryCopy];
              const newHistorySessions = state.historySessions.filter(s => s.sessionUniqueId !== sessionInHistory.sessionUniqueId);

              return {
                currentSessions: newCurrentSessions,
                historySessions: newHistorySessions,
                activeSessionId: sessionInHistoryCopy.id // 使用agentId作为activeSessionId
              };
            }
          });

          // 检查切换后的会话的智能体状态
          const historyAgentId = sessionInHistory.agentId;
          if (historyAgentId && historyAgentId !== DEFAULT_AGENT_ID) {
            // 异步检查智能体状态
            get().checkAgentStatus(historyAgentId);
          } else {
            // 清除智能体状态（普通会话）
            set({ currentAgentStatus: null });
          }
        }
        // 点击当前会话时什么都不做
      },

      deleteSession: (sessionUniqueId: string) => {
        set((state) => {
          // 找到要删除的会话
          const sessionToDelete = [...state.currentSessions, ...state.historySessions]
            .find(s => s.sessionUniqueId === sessionUniqueId);

          if (!sessionToDelete) return state;

          // 从当前会话中删除
          const newCurrentSessions = state.currentSessions.filter(s => s.sessionUniqueId !== sessionUniqueId);
          // 从历史会话中删除
          const newHistorySessions = state.historySessions.filter(s => s.sessionUniqueId !== sessionUniqueId);

          // 如果删除的是当前活跃会话，选择新的活跃会话
          let newActiveSessionId = state.activeSessionId;
          if (state.activeSessionId === sessionToDelete.id) {
            newActiveSessionId = newCurrentSessions.length > 0 ? newCurrentSessions[0].id : null;
          }

          return {
            currentSessions: newCurrentSessions,
            historySessions: newHistorySessions,
            activeSessionId: newActiveSessionId,
          };
        });
      },

      updateSessionTitle: (sessionUniqueId: string, title: string) => {
        set((state) => {
          const updateSession = (session: ChatSession) =>
            session.sessionUniqueId === sessionUniqueId
              ? { ...session, title, updatedAt: new Date().toISOString() }
              : session;

          return {
            currentSessions: state.currentSessions.map(updateSession),
            historySessions: state.historySessions.map(updateSession).sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            ),
          };
        });
      },

      updateSessionModel: (sessionUniqueId: string, provider: AIProvider, model: string) => {
        set((state) => {
          const updateSession = (session: ChatSession) =>
            session.sessionUniqueId === sessionUniqueId
              ? { ...session, provider, model, updatedAt: new Date().toISOString() }
              : session;

          return {
            currentSessions: state.currentSessions.map(updateSession),
            historySessions: state.historySessions.map(updateSession).sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            ),
          };
        });
      },

      sendMessage: async (content: string, files?: FileInfo[]) => {
        const { currentSessions, activeSessionId } = get();

        // 获取当前活跃会话
        let activeSession = currentSessions.find(s => s.id === activeSessionId);

        // 如果没有活跃会话，创建一个新会话
        if (!activeSession) {
          const newSessionId = get().createSession();
          const updatedState = get();
          activeSession = updatedState.currentSessions.find(s => s.id === newSessionId);
          if (!activeSession) return;
        }

        // 检查智能体使用权限（仅对智能体会话）
        if (activeSession.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
          const authStore = useAuthStore.getState();
          const user = authStore.user;

          if (user) {
            try {
              const remainingResult = await agentRemainingApi.checkCanUse(user.id, activeSession.agentId);

              if (remainingResult.success && remainingResult.data) {
                const { canUse, reason, remainingCount, status } = remainingResult.data;

                if (!canUse) {
                  // 用户无法使用该智能体
                  const errorMessage: Message = {
                    id: generateId(),
                    role: 'assistant',
                    content: reason === 'trial_exhausted'
                      ? `抱歉，您的试用次数已用完。该智能体的试用次数为 ${remainingResult.data.trialCount} 次，您已全部使用完毕。请考虑购买订阅以继续使用。`
                      : '抱歉，您暂时无法使用该智能体。',
                    timestamp: Date.now(),
                    status: 'sent',
                  };

                  get().addMessage(errorMessage);
                  return;
                }

                // 显示剩余次数提醒（仅试用用户）
                if (!remainingResult.data.hasSubscription && remainingCount !== null && remainingCount <= 3) {
                  const reminderMessage: Message = {
                    id: generateId(),
                    role: 'assistant',
                    content: `💡 提醒：您还有 ${remainingCount} 次试用机会。`,
                    timestamp: Date.now(),
                    status: 'sent',
                  };

                  get().addMessage(reminderMessage);
                }
              }
            } catch (error) {
              console.error('检查智能体使用权限失败:', error);
              // 检查失败时继续执行，不阻断用户体验
            }
          }
        }

        // 检查是否是智能体会话的第一条消息，如果是则先添加智能体描述
        const isFirstMessage = activeSession.messages.length === 0;
        if (isFirstMessage && activeSession.agentId && activeSession.agentId !== DEFAULT_AGENT_ID && activeSession.agentDescription) {
          const agentName = activeSession.agentName || '智能体';
          const agentDescriptionMessage: Message = {
            id: `agent_desc_${Date.now()}`,
            role: 'assistant',
            content: `你好！我是 ${agentName}。\n\n${activeSession.agentDescription}`,
            timestamp: Date.now(),
            status: 'sent',
            isAgentDescription: true,
          };
          get().addMessage(agentDescriptionMessage);
        }

        // 创建用户消息
        const userMessage: Message = {
          id: generateId(),
          role: 'user',
          content,
          timestamp: Date.now(),
          files,
          status: 'sent',
        };

        // 添加用户消息
        get().addMessage(userMessage);

        // 设置加载状态
        set({ isLoading: true });

        // 创建助手消息占位符
        const assistantMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: '',
          timestamp: Date.now(),
          status: 'sending',
        };

        get().addMessage(assistantMessage);

        try {
          // 获取当前用户选择的AI提供商配置
          const aiProvidersStore = useAIProviderConfigsStore.getState();
          const currentProvider = aiProvidersStore.userSettings?.currentProvider || activeSession.provider || 'google';
          const currentModel = aiProvidersStore.userSettings?.currentModel || activeSession.model || 'gemini-2.0-flash';



          // 构建对话历史（最近10条消息）
          const conversationHistory = activeSession.messages
            .slice(-10)
            .filter((msg: Message) => msg.status === 'sent')
            .map((msg: Message) => ({
              role: msg.role,
              content: msg.content,
            }));

          // 检查是否是智能体会话
          let agent = null;
          if (activeSession.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
            // 获取智能体信息
            const agentStore = useAgentStore.getState();
            agent = agentStore.agents.find(a => a.id === activeSession.agentId);
          }

          // 调用AI服务
          const result = await aiService.sendMessage(currentProvider, content, files, conversationHistory, agent || undefined);

          if (result.success && result.data) {
            // 更新助手消息
            get().updateMessage(assistantMessage.id, {
              content: result.data,
              status: 'sent',
            });

            // 记录智能体使用次数（仅在智能体对话且API调用成功时）
            if (agent && activeSession.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
              try {
                const authStore = useAuthStore.getState();
                const currentUser = authStore.user;

                if (currentUser?.id) {
                  // 检查用户是否有有效的定时订阅
                  let expiryDate = null;
                  try {
                    const subscriptionResponse = await fetch(`/api/agent-subscription?userId=${currentUser.id}&agentId=${activeSession.agentId}`);
                    if (subscriptionResponse.ok) {
                      const subscriptionData = await subscriptionResponse.json();
                      if (subscriptionData.success && subscriptionData.data.length > 0) {
                        // 查找有效的定时订阅
                        const activeTimedSubscription = subscriptionData.data.find((sub: any) =>
                          sub.status === 'active' &&
                          sub.planType === 'time_based' &&
                          sub.endDate &&
                          new Date(sub.endDate) > new Date()
                        );

                        if (activeTimedSubscription) {
                          expiryDate = activeTimedSubscription.endDate;
                        }
                      }
                    }
                  } catch (subscriptionError) {
                    console.warn('获取订阅信息失败:', subscriptionError);
                  }

                  await agentUsageApi.recordUsage({
                    userId: currentUser.id,
                    agentId: activeSession.agentId,
                    sessionId: activeSession.sessionUniqueId,
                    usageType: 'chat',
                    tokensUsed: 0, // 可以后续根据实际token使用量更新
                    cost: 0, // 可以后续根据实际成本更新
                    usageCount: 1, // 本次使用次数
                    expiryDate: expiryDate, // 截止时间（如果有定时订阅）
                  });
                }
              } catch (usageError) {
                console.error('记录智能体使用次数失败:', usageError);
                // 不影响主要功能，只记录错误
              }
            }
          } else {
            // API 调用失败
            get().updateMessage(assistantMessage.id, {
              content: `抱歉，生成回复时出现错误：${result.error || '未知错误'}`,
              status: 'error',
            });
          }
        } catch (error) {
          console.error('Send message error:', error);
          get().updateMessage(assistantMessage.id, {
            content: `抱歉，发送消息时出现错误：${error instanceof Error ? error.message : '未知错误'}`,
            status: 'error',
          });
        } finally {
          set({ isLoading: false });
        }

        // 清空上传的文件
        get().clearUploadedFiles();

        // 自动更新会话标题（如果是第一条消息且不是智能体会话）
        if (activeSession.messages.length === 0) {
          // 只有在非智能体会话或者没有智能体名称时才自动设置标题
          if (!activeSession.agentId || activeSession.agentId === DEFAULT_AGENT_ID || !activeSession.agentName) {
            const title = content.length > 20 ? content.substring(0, 20) + '...' : content;
            get().updateSessionTitle(activeSession.sessionUniqueId, title);
          }
        }
      },

      addMessage: (message: Message) => {
        set((state) => {
          const { currentSessions, activeSessionId } = state;
          const activeSession = currentSessions.find(s => s.id === activeSessionId);

          if (!activeSession) return state;

          const updatedSession = {
            ...activeSession,
            messages: [...activeSession.messages, message],
            updatedAt: new Date().toISOString(),
          };

          return {
            currentSessions: currentSessions.map(session =>
              session.sessionUniqueId === updatedSession.sessionUniqueId ? updatedSession : session
            ),
            historySessions: state.historySessions.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            ),
          };
        });
      },

      updateMessage: (messageId: string, updates: Partial<Message>) => {
        set((state) => {
          const { currentSessions, activeSessionId } = state;
          const activeSession = currentSessions.find(s => s.id === activeSessionId);

          if (!activeSession) return state;

          const updatedSession = {
            ...activeSession,
            messages: activeSession.messages.map((message: Message) =>
              message.id === messageId ? { ...message, ...updates } : message
            ),
            updatedAt: new Date().toISOString(),
          };

          return {
            currentSessions: currentSessions.map(session =>
              session.sessionUniqueId === updatedSession.sessionUniqueId ? updatedSession : session
            ),
            historySessions: state.historySessions.sort((a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            ),
          };
        });
      },

      deleteMessage: (messageId: string) => {
        set((state) => {
          const { currentSessions, activeSessionId } = state;
          const activeSession = currentSessions.find(s => s.id === activeSessionId);

          if (!activeSession) return state;

          const updatedSession = {
            ...activeSession,
            messages: activeSession.messages.filter((message: Message) => message.id !== messageId),
            updatedAt: new Date().toISOString(),
          };

          return {
            currentSessions: currentSessions.map(session =>
              session.sessionUniqueId === updatedSession.sessionUniqueId ? updatedSession : session
            ),
          };
        });
      },

      regenerateMessage: async (messageId: string) => {
        const { currentSessions, activeSessionId } = get();
        const activeSession = currentSessions.find(s => s.id === activeSessionId);
        if (!activeSession) return;

        const messageIndex = activeSession.messages.findIndex((m: Message) => m.id === messageId);
        if (messageIndex === -1 || activeSession.messages[messageIndex].role !== 'assistant') return;

        // 找到对应的用户消息
        const userMessageIndex = messageIndex - 1;
        if (userMessageIndex < 0 || activeSession.messages[userMessageIndex].role !== 'user') return;

        const userMessage = activeSession.messages[userMessageIndex];

        // 更新消息状态为重新生成中
        get().updateMessage(messageId, { status: 'sending', content: '' });
        set({ isLoading: true });

        try {
          // 获取当前用户选择的AI提供商配置
          const aiProvidersStore = useAIProviderConfigsStore.getState();
          const currentProvider = aiProvidersStore.userSettings?.currentProvider || activeSession.provider || 'google';
          const currentModel = aiProvidersStore.userSettings?.currentModel || activeSession.model || 'gemini-2.0-flash';


          // 构建对话历史（到用户消息为止）
          const conversationHistory = activeSession.messages
            .slice(0, userMessageIndex)
            .filter((msg: Message) => msg.status === 'sent')
            .map((msg: Message) => ({
              role: msg.role,
              content: msg.content,
            }));

          // 检查是否是智能体会话
          let agent = null;
          if (activeSession.agentId && activeSession.agentId !== DEFAULT_AGENT_ID) {
            // 获取智能体信息
            const agentStore = useAgentStore.getState();
            agent = agentStore.agents.find(a => a.id === activeSession.agentId);
          }

          // 重新调用 AI 服务
          const result = await aiService.sendMessage(
            currentProvider,
            userMessage.content,
            userMessage.files,
            conversationHistory,
            agent || undefined
          );

          if (result.success && result.data) {
            get().updateMessage(messageId, {
              content: result.data,
              status: 'sent',
              timestamp: Date.now(),
            });
          } else {
            get().updateMessage(messageId, {
              content: `重新生成失败：${result.error || '未知错误'}`,
              status: 'error',
              timestamp: Date.now(),
            });
          }
        } catch (error) {
          console.error('Regenerate message error:', error);
          get().updateMessage(messageId, {
            content: `重新生成失败：${error instanceof Error ? error.message : '未知错误'}`,
            status: 'error',
            timestamp: Date.now(),
          });
        } finally {
          set({ isLoading: false });
        }
      },

      // 清空当前会话的消息（保留智能体描述消息）
      clearSessionMessages: () => {
        const { currentSessions, activeSessionId } = get();
        const activeSession = currentSessions.find(s => s.id === activeSessionId);
        if (!activeSession) return;

        // 只保留智能体描述消息（role为'assistant'且content包含智能体描述的消息）
        const agentDescriptionMessage = activeSession.messages.find(
          (msg: Message) => msg.role === 'assistant' && msg.isAgentDescription
        );

        const updatedSessions = currentSessions.map(session => {
          if (session.id === activeSessionId) {
            return {
              ...session,
              messages: agentDescriptionMessage ? [agentDescriptionMessage] : [],
              updatedAt: new Date().toISOString(),
            };
          }
          return session;
        });

        set({ currentSessions: updatedSessions });
      },

      // 清空所有历史会话
      clearHistorySessions: () => {
        set({ historySessions: [] });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setUploadedFiles: (files: FileInfo[]) => {
        set({ uploadedFiles: files });
      },

      addUploadedFile: (file: FileInfo) => {
        set((state) => ({
          uploadedFiles: [...state.uploadedFiles, file],
        }));
      },

      removeUploadedFile: (fileId: string) => {
        set((state) => ({
          uploadedFiles: state.uploadedFiles.filter(f => f.id !== fileId),
        }));
      },

      clearUploadedFiles: () => {
        set({ uploadedFiles: [] });
      },

      setCurrentMessage: (_content: string) => {
        // 如果没有活跃会话，创建一个新会话
        const { currentSessions, activeSessionId } = get();
        const activeSession = currentSessions.find(s => s.id === activeSessionId);
        if (!activeSession) {
          get().createSession();
        }

        // 这个方法主要用于从提示词管理器设置消息内容
        // 实际的消息发送由 sendMessage 处理
        // 这里可以用于预填充输入框或其他用途
        // TODO: 实现消息内容设置逻辑
      },

      checkAgentStatus: async (agentId: string) => {
        try {
          const result = await agentStatusApi.checkStatus(agentId);

          if (result.success && result.data) {
            set({
              currentAgentStatus: {
                agentId,
                exists: result.data.exists,
                enabled: result.data.enabled,
                name: result.data.name,
                reason: result.data.reason || null,
              }
            });
          } else {
            // API调用失败，设置为不可用状态
            set({
              currentAgentStatus: {
                agentId,
                exists: false,
                enabled: false,
                name: null,
                reason: 'api_error',
              }
            });
          }
        } catch (error) {
          console.error('检查智能体状态失败:', error);
          // 错误情况下设置为不可用状态
          set({
            currentAgentStatus: {
              agentId,
              exists: false,
              enabled: false,
              name: null,
              reason: 'check_failed',
            }
          });
        }
      },

      clearAll: () => {
        set({
          currentSessions: [],
          historySessions: [],
          activeSessionId: null,
          isLoading: false,
          uploadedFiles: [],
          currentAgentStatus: null,
        });
      },
    }),
    {
      name: 'chat-store',
      partialize: (state) => ({
        currentSessions: state.currentSessions,
        historySessions: state.historySessions,
        activeSessionId: state.activeSessionId,
      }),
    }
  )
);